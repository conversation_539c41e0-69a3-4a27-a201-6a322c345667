/** biome-ignore-all lint/suspicious/noDuplicateProperties: <explanation> */
*,
:before,
:after {
	box-sizing: border-box;
	border-width: 0;
	border-style: solid;
	border-color: currentColor;
}
:before,
:after {
	--tw-content: "";
}
html {
	line-height: 1.5;
	-webkit-text-size-adjust: 100%;
	-moz-tab-size: 4;
	tab-size: 4;
	font-family:
		ui-sans-serif,
		system-ui,
		-apple-system,
		BlinkMacSystemFont,
		Segoe UI,
		Roboto,
		Helvetica Neue,
		Arial,
		Noto Sans,
		sans-serif,
		"Apple Color Emoji",
		"Segoe UI Emoji",
		Segoe UI Symbol,
		"Noto Color Emoji";
}
body {
	margin: 0;
	line-height: inherit;
}
hr {
	height: 0;
	color: inherit;
	border-top-width: 1px;
}
abbr[title] {
	-webkit-text-decoration: underline dotted;
	text-decoration: underline dotted;
}
h1,
h2,
h3,
h4,
h5,
h6 {
	font-size: inherit;
	font-weight: inherit;
}
a {
	color: inherit;
	text-decoration: inherit;
}
b,
strong {
	font-weight: bolder;
}
code,
kbd,
samp,
pre {
	font-family:
		ui-monospace,
		SFMono-Regular,
		Menlo,
		Monaco,
		Consolas,
		Liberation Mono,
		Courier New,
		monospace;
	font-size: 1em;
}
small {
	font-size: 80%;
}
sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}
sub {
	bottom: -0.25em;
}
sup {
	top: -0.5em;
}
table {
	text-indent: 0;
	border-color: inherit;
	border-collapse: collapse;
}
button,
input,
optgroup,
select,
textarea {
	font-family: inherit;
	font-size: 100%;
	line-height: inherit;
	color: inherit;
	margin: 0;
	padding: 0;
}
button,
select {
	text-transform: none;
}
button,
[type="button"],
[type="reset"],
[type="submit"] {
	-webkit-appearance: button;
	background-color: transparent;
	background-image: none;
}
:-moz-focusring {
	outline: auto;
}
:-moz-ui-invalid {
	box-shadow: none;
}
progress {
	vertical-align: baseline;
}
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
	height: auto;
}
[type="search"] {
	-webkit-appearance: textfield;
	outline-offset: -2px;
}
::-webkit-search-decoration {
	-webkit-appearance: none;
}
::-webkit-file-upload-button {
	-webkit-appearance: button;
	font: inherit;
}
summary {
	display: list-item;
}
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
	margin: 0;
}
fieldset {
	margin: 0;
	padding: 0;
}
legend {
	padding: 0;
}
ol,
ul,
menu {
	list-style: none;
	margin: 0;
	padding: 0;
}
textarea {
	resize: vertical;
}
input::placeholder,
textarea::placeholder {
	opacity: 1;
	color: #9ca3af;
}
button,
[role="button"] {
	cursor: pointer;
}
:disabled {
	cursor: default;
}
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
	display: block;
	vertical-align: middle;
}
img,
video {
	max-width: 100%;
	height: auto;
}
[hidden] {
	display: none;
}
.transform {
	--tw-translate-x: 0;
	--tw-translate-y: 0;
	--tw-rotate: 0;
	--tw-skew-x: 0;
	--tw-skew-y: 0;
	--tw-scale-x: 1;
	--tw-scale-y: 1;
	--tw-transform: translateX(var(--tw-translate-x))
		translateY(var(--tw-translate-y)) rotate(var(--tw-rotate))
		skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
		scaleY(var(--tw-scale-y));
}
.border,
.border-2,
.border-b,
.border-r {
	--tw-border-opacity: 1;
	border-color: rgb(229 231 235 / var(--tw-border-opacity));
}
.shadow-lg,
.hover\:shadow-lg {
	--tw-ring-offset-shadow: 0 0 #0000;
	--tw-ring-shadow: 0 0 #0000;
	--tw-shadow: 0 0 #0000;
	--tw-shadow-colored: 0 0 #0000;
}
.focus\:ring-2 {
	--tw-ring-inset: var(--tw-empty, );
	--tw-ring-offset-width: 0px;
	--tw-ring-offset-color: #fff;
	--tw-ring-color: rgb(59 130 246 / 0.5);
	--tw-ring-offset-shadow: 0 0 #0000;
	--tw-ring-shadow: 0 0 #0000;
	--tw-shadow: 0 0 #0000;
	--tw-shadow-colored: 0 0 #0000;
}
.container {
	width: 100%;
}
@media (min-width: 640px) {
	.container {
		max-width: 640px;
	}
}
@media (min-width: 768px) {
	.container {
		max-width: 768px;
	}
}
@media (min-width: 1024px) {
	.container {
		max-width: 1024px;
	}
}
@media (min-width: 1280px) {
	.container {
		max-width: 1280px;
	}
}
@media (min-width: 1536px) {
	.container {
		max-width: 1536px;
	}
}
.fixed {
	position: fixed;
}
.sticky {
	position: -webkit-sticky;
	position: sticky;
}
.inset-0 {
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
}
.top-0 {
	top: 0;
}
.left-0 {
	left: 0;
}
.z-20 {
	z-index: 20;
}
.z-50 {
	z-index: 50;
}
.my-20 {
	margin-top: 5rem;
	margin-bottom: 5rem;
}
.ml-10 {
	margin-left: 2.5rem;
}
.mt-1 {
	margin-top: 0.25rem;
}
.ml-9 {
	margin-left: 2.25rem;
}
.mt-7 {
	margin-top: 1.75rem;
}
.mt-4 {
	margin-top: 1rem;
}
.ml-\[300px\] {
	margin-left: 300px;
}
.mt-6 {
	margin-top: 1.5rem;
}
.mr-2 {
	margin-right: 0.5rem;
}
.mb-4 {
	margin-bottom: 1rem;
}
.mt-5 {
	margin-top: 1.25rem;
}
.mt-8 {
	margin-top: 2rem;
}
.mb-20 {
	margin-bottom: 5rem;
}
.mt-2 {
	margin-top: 0.5rem;
}
.mt-10 {
	margin-top: 2.5rem;
}
.ml-2 {
	margin-left: 0.5rem;
}
.flex {
	display: flex;
}
.table {
	display: table;
}
.h-16 {
	height: 4rem;
}
.h-10 {
	height: 2.5rem;
}
.h-screen {
	height: 100vh;
}
.h-\[100px\] {
	height: 100px;
}
.h-\[400px\] {
	height: 400px;
}
.h-\[300px\] {
	height: 300px;
}
.h-full {
	height: 100%;
}
.h-4 {
	height: 1rem;
}
.h-\[200px\] {
	height: 200px;
}
.w-10 {
	width: 2.5rem;
}
.w-\[300px\] {
	width: 300px;
}
.w-\[250px\] {
	width: 250px;
}
.w-full {
	width: 100%;
}
.w-\[500px\] {
	width: 500px;
}
.w-\[400px\] {
	width: 400px;
}
.w-\[90\%\] {
	width: 90%;
}
.w-4 {
	width: 1rem;
}
.w-\[600px\] {
	width: 600px;
}
.w-\[80\%\] {
	width: 80%;
}
.min-w-full {
	min-width: 100%;
}
.min-w-\[400px\] {
	min-width: 400px;
}
.max-w-\[300px\] {
	max-width: 300px;
}
.max-w-2xl {
	max-width: 42rem;
}
.transform {
	transform: var(--tw-transform);
}
.cursor-pointer {
	cursor: pointer;
}
.flex-row {
	flex-direction: row;
}
.flex-col {
	flex-direction: column;
}
.items-start {
	align-items: flex-start;
}
.items-center {
	align-items: center;
}
.justify-start {
	justify-content: flex-start;
}
.justify-center {
	justify-content: center;
}
.justify-between {
	justify-content: space-between;
}
.justify-around {
	justify-content: space-around;
}
.gap-4 {
	gap: 1rem;
}
.gap-52 {
	gap: 13rem;
}
.gap-8 {
	gap: 2rem;
}
.gap-2 {
	gap: 0.5rem;
}
.gap-6 {
	gap: 1.5rem;
}
.gap-10 {
	gap: 2.5rem;
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(1rem * var(--tw-space-x-reverse));
	margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.rounded-full {
	border-radius: 9999px;
}
.rounded-md {
	border-radius: 0.375rem;
}
.rounded-lg {
	border-radius: 0.5rem;
}
.rounded {
	border-radius: 0.25rem;
}
.rounded-sm {
	border-radius: 0.125rem;
}
.rounded-xl {
	border-radius: 0.75rem;
}
.border {
	border-width: 1px;
}
.border-2 {
	border-width: 2px;
}
.border-b {
	border-bottom-width: 1px;
}
.border-r {
	border-right-width: 1px;
}
.border-none {
	border-style: none;
}
.border-gray-200 {
	--tw-border-opacity: 1;
	border-color: rgb(229 231 235 / var(--tw-border-opacity));
}
.border-gray-700 {
	--tw-border-opacity: 1;
	border-color: rgb(55 65 81 / var(--tw-border-opacity));
}
.border-black {
	--tw-border-opacity: 1;
	border-color: rgb(0 0 0 / var(--tw-border-opacity));
}
.border-gray-300 {
	--tw-border-opacity: 1;
	border-color: rgb(209 213 219 / var(--tw-border-opacity));
}
.border-blue-500 {
	--tw-border-opacity: 1;
	border-color: rgb(59 130 246 / var(--tw-border-opacity));
}
.border-white {
	--tw-border-opacity: 1;
	border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.bg-white {
	--tw-bg-opacity: 1;
	background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-blue-500 {
	--tw-bg-opacity: 1;
	background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}
.bg-black {
	--tw-bg-opacity: 1;
	background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}
.bg-gray-300 {
	--tw-bg-opacity: 1;
	background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}
.bg-transparent {
	background-color: transparent;
}
.bg-blue-600 {
	--tw-bg-opacity: 1;
	background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}
.bg-gray-200 {
	--tw-bg-opacity: 1;
	background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.bg-slate-100 {
	--tw-bg-opacity: 1;
	background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}
.bg-opacity-50 {
	--tw-bg-opacity: 0.5;
}
.p-20 {
	padding: 5rem;
}
.p-10 {
	padding: 2.5rem;
}
.p-2 {
	padding: 0.5rem;
}
.p-6 {
	padding: 1.5rem;
}
.p-1 {
	padding: 0.25rem;
}
.p-4 {
	padding: 1rem;
}
.p-8 {
	padding: 2rem;
}
.py-4 {
	padding-top: 1rem;
	padding-bottom: 1rem;
}
.px-4 {
	padding-left: 1rem;
	padding-right: 1rem;
}
.py-2 {
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
}
.px-6 {
	padding-left: 1.5rem;
	padding-right: 1.5rem;
}
.px-10 {
	padding-left: 2.5rem;
	padding-right: 2.5rem;
}
.px-7 {
	padding-left: 1.75rem;
	padding-right: 1.75rem;
}
.py-3 {
	padding-top: 0.75rem;
	padding-bottom: 0.75rem;
}
.pr-10 {
	padding-right: 2.5rem;
}
.pt-4 {
	padding-top: 1rem;
}
.pb-4 {
	padding-bottom: 1rem;
}
.text-center {
	text-align: center;
}
.text-2xl {
	font-size: 1.5rem;
	line-height: 2rem;
}
.text-sm {
	font-size: 0.875rem;
	line-height: 1.25rem;
}
.text-xl {
	font-size: 1.25rem;
	line-height: 1.75rem;
}
.text-6xl {
	font-size: 3.75rem;
	line-height: 1;
}
.text-8xl {
	font-size: 6rem;
	line-height: 1;
}
.text-4xl {
	font-size: 2.25rem;
	line-height: 2.5rem;
}
.text-3xl {
	font-size: 1.875rem;
	line-height: 2.25rem;
}
.font-bold {
	font-weight: 700;
}
.font-medium {
	font-weight: 500;
}
.font-black {
	font-weight: 900;
}
.text-red-500 {
	--tw-text-opacity: 1;
	color: rgb(239 68 68 / var(--tw-text-opacity));
}
.text-white {
	--tw-text-opacity: 1;
	color: rgb(255 255 255 / var(--tw-text-opacity));
}
.text-black {
	--tw-text-opacity: 1;
	color: rgb(0 0 0 / var(--tw-text-opacity));
}
.text-blue-600 {
	--tw-text-opacity: 1;
	color: rgb(37 99 235 / var(--tw-text-opacity));
}
.shadow-lg {
	--tw-shadow:
		0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
	--tw-shadow-colored:
		0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px
		var(--tw-shadow-color);
	box-shadow:
		var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
		var(--tw-shadow);
}
body {
	margin: 0;
	font-family:
		-apple-system,
		BlinkMacSystemFont,
		Segoe UI,
		Roboto,
		Oxygen,
		Ubuntu,
		Cantarell,
		Fira Sans,
		Droid Sans,
		Helvetica Neue,
		sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
code {
	font-family:
		source-code-pro,
		Menlo,
		Monaco,
		Consolas,
		Courier New,
		monospace;
}
.hover\:text-blue-500:hover {
	--tw-text-opacity: 1;
	color: rgb(59 130 246 / var(--tw-text-opacity));
}
.hover\:text-gray-600:hover {
	--tw-text-opacity: 1;
	color: rgb(75 85 99 / var(--tw-text-opacity));
}
.hover\:shadow-lg:hover {
	--tw-shadow:
		0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
	--tw-shadow-colored:
		0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px
		var(--tw-shadow-color);
	box-shadow:
		var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
		var(--tw-shadow);
}
.focus\:outline-none:focus {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.focus\:ring-2:focus {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
		var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0
		calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow:
		var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
		var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-gray-300:focus {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}
