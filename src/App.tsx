import { useEffect } from "react";
import { Route, BrowserRouter as Router, Routes } from "react-router-dom";
import "./index.css";

// Import test utility for debugging (only in development)
if (import.meta.env.DEV) {
  import('./utils/testXendit');
  import('./utils/transactionSync');
  import('./utils/cleanupTransactions');
}

import Affiliate from "./pages/Affiliate";
import Ai from "./pages/Ai";
import BeliEmas from "./pages/BeliEmas";
import Billing from "./pages/Billing";
import Dashboard from "./pages/Dashboard";
import Home from "./pages/Home";
import Login from "./pages/Login";
import Services from "./pages/Service";
import Setting from "./pages/Setting";
import Support from "./pages/Support";
import Templates from "./pages/Templates";
import { supabase } from "./supabase";

function App() {
	useEffect(() => {
		// Handle OAuth callback and clean up URL
		const handleAuthCallback = async () => {
			const { data } = await supabase.auth.getSession();

			if (data.session && window.location.hash) {
				// Clean up the URL by removing the hash
				window.history.replaceState(null, "", window.location.pathname);
			}
		};

		handleAuthCallback();
	}, []);

	return (
		<Router>
			<Routes>
				<Route path="/" element={<Home />} />
				<Route path="/templates" element={<Templates />} />
				<Route path="/login" element={<Login />}></Route>

				<Route element={<Dashboard />}>
					<Route path="/dashboard/services" element={<Services />}></Route>
					<Route path="/dashboard/billing" element={<Billing />}></Route>
					<Route path="/dashboard/ai" element={<Ai />}></Route>
					<Route path="/dashboard/affiliate" element={<Affiliate />} />
					<Route path="/dashboard/setting" element={<Setting />} />
					<Route path="/dashboard/support" element={<Support />} />
					<Route path="/dashboard/beli-emas" element={<BeliEmas />} />
				</Route>
			</Routes>
		</Router>
	);
}

export default App;
