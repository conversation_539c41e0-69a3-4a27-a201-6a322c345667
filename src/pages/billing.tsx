import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import BalanceCard from "../components/BalanceCard";
import PaymentStatusChecker from "../components/PaymentStatusChecker";
import TopUpModal from "../components/TopUpModal";
import TransactionTable from "../components/TransactionTable";
import { supabase } from "../supabase";

function Billing() {
	const [modalOpen, setModalOpen] = useState(false);
	const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
	const [loading, setLoading] = useState<boolean>(true);
	const [refreshKey, setRefreshKey] = useState<number>(0);
	const navigate = useNavigate();

	// Debug: Monitor auth state changes
	useEffect(() => {
		console.log('🔍 Billing.tsx: isAuthenticated changed to:', isAuthenticated);
	}, [isAuthenticated]);

	useEffect(() => {
		const checkAuth = async () => {
			try {
				const {
					data: { session },
				} = await supabase.auth.getSession();

				if (!session?.user) {
					navigate("/login");
					return;
				}

				setIsAuthenticated(true);
			} catch (error) {
				console.error("Error checking authentication:", error);
				navigate("/login");
			} finally {
				setLoading(false);
			}
		};

		checkAuth();
	}, [navigate]);

	// Check for payment status in URL (simple approach)
	useEffect(() => {
		const urlParams = new URLSearchParams(window.location.search);
		const paymentStatus = urlParams.get('payment');

		if (paymentStatus === 'success') {
			// Refresh data after successful payment
			setRefreshKey(prev => prev + 1);
			// Clean up URL
			window.history.replaceState({}, '', window.location.pathname);
		}
	}, []);

	const handleTopUp = (amount: number) => {
		console.log(`Top-up initiated for amount: ${amount}`);
		// Modal will handle the actual payment flow
		// This callback is mainly for logging/analytics
	};

	if (loading) {
		return (
			<div className="ml-[300px] p-6">
				<div className="flex items-center justify-center h-64">
					<div className="animate-pulse text-lg">Loading...</div>
				</div>
			</div>
		);
	}

	if (!isAuthenticated) {
		return (
			<div className="ml-[300px] p-6">
				<div className="flex items-center justify-center h-64">
					<div className="text-lg text-red-600">
						Authentication required. Redirecting to login...
					</div>
				</div>
			</div>
		);
	}

	return (
		<>
			<div className="ml-[300px] p-6">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-2xl font-bold">Billing</h1>
						<p>Manage your balance and view transaction history</p>
					</div>
					<div className="flex gap-4">
						<button
							type="button"
							className="bg-gray-300 text-black px-4 py-2 rounded-lg mr-2"
						>
							redeem
						</button>
						<button
							type="button"
							className="bg-blue-500 text-white px-4 py-2 rounded-lg"
							onClick={() => setModalOpen(true)}
						>
							Add credit
						</button>
					</div>
				</div>

				<TopUpModal
					isOpen={modalOpen}
					onClose={() => setModalOpen(false)}
					onTopUp={handleTopUp}
				/>

				<PaymentStatusChecker onStatusUpdate={() => setRefreshKey(prev => prev + 1)} />

				{/* Transaction Sync Tools - Development Only */}
				{import.meta.env.DEV && (
					<div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
						<h3 className="text-lg font-semibold text-blue-800 mb-2">
							🔧 Transaction Sync Tools (Development)
						</h3>
						<p className="text-blue-700 mb-3 text-sm">
							Use these tools to fix missing transactions and update description formats.
						</p>
						<div className="flex flex-wrap gap-2 mb-3">
							<button
								type="button"
								onClick={async () => {
									if ((window as any).syncMissingTransactions) {
										const result = await (window as any).syncMissingTransactions();
										alert(`Sync completed: ${result.created} created, ${result.updated} updated`);
										window.location.reload();
									} else {
										alert('Sync utility not loaded. Check console.');
									}
								}}
								className="px-3 py-2 rounded text-sm bg-blue-500 hover:bg-blue-600 text-white"
							>
								Sync Missing Transactions
							</button>
							<button
								type="button"
								onClick={async () => {
									if ((window as any).fixTransactionDescriptions) {
										const result = await (window as any).fixTransactionDescriptions();
										alert(`Descriptions fixed: ${result.updated} updated`);
										window.location.reload();
									} else {
										alert('Fix utility not loaded. Check console.');
									}
								}}
								className="px-3 py-2 rounded text-sm bg-green-500 hover:bg-green-600 text-white"
							>
								Fix Descriptions
							</button>
							<button
								type="button"
								onClick={async () => {
									if ((window as any).fullCleanup) {
										const result = await (window as any).fullCleanup();
										alert(`Cleanup completed: ${result.duplicatesRemoved} duplicates removed, balance ${result.balanceFixed ? 'fixed' : 'already correct'}`);
										window.location.reload();
									} else {
										alert('Cleanup utility not loaded. Check console.');
									}
								}}
								className="px-3 py-2 rounded text-sm bg-red-500 hover:bg-red-600 text-white"
							>
								🧹 Full Cleanup
							</button>
							<button
								type="button"
								onClick={async () => {
									if ((window as any).recalculateBalance) {
										const result = await (window as any).recalculateBalance();
										alert(`Balance recalculated: Old: Rp ${result.oldBalance.toLocaleString('id-ID')}, New: Rp ${result.newBalance.toLocaleString('id-ID')}`);
										window.location.reload();
									} else {
										alert('Recalculate utility not loaded. Check console.');
									}
								}}
								className="px-3 py-2 rounded text-sm bg-purple-500 hover:bg-purple-600 text-white"
							>
								💰 Fix Balance
							</button>
						</div>
						<div className="text-xs text-blue-600">
							<p><strong>Sync Missing:</strong> Creates transaction records for completed payments</p>
							<p><strong>Fix Descriptions:</strong> Updates old format descriptions to new format</p>
							<p><strong>Full Cleanup:</strong> Removes duplicate transactions and fixes balance calculation</p>
						</div>
					</div>
				)}

				<BalanceCard key={`balance-${refreshKey}`} />

				<TransactionTable key={`transactions-${refreshKey}`} />
			</div>
		</>
	);
}

export default Billing;