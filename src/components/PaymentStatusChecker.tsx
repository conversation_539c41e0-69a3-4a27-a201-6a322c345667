import { useEffect, useState } from 'react';
import { supabase } from '../supabase';
import { completePayment } from '../utils/balanceUtils';

interface PaymentStatusCheckerProps {
  onStatusUpdate?: () => void;
}

function PaymentStatusChecker({ onStatusUpdate }: PaymentStatusCheckerProps) {
  const [pendingPayments, setPendingPayments] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    checkPendingPayments();
  }, []);

  const checkPendingPayments = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) return;

      const { data: payments } = await supabase
        .from('payments')
        .select('*')
        .eq('user_id', session.user.id)
        .eq('status', 'pending')
        .order('created_at', { ascending: false })
        .limit(5);

      setPendingPayments(payments || []);
    } catch (error) {
      console.error('Error checking pending payments:', error);
    }
  };

  const manuallyCompletePendingPayment = async (payment: any) => {
    setLoading(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user) return;

      console.log('🔧 Manually completing payment:', payment);

      // Use the utility function to complete the payment
      await completePayment(
        payment.id,
        session.user.id,
        payment.amount,
        payment.invoice_id
      );

      alert('✅ Payment completed successfully! Refreshing page...');

      // Refresh the page to show updated data
      if (onStatusUpdate) onStatusUpdate();
      window.location.reload();

    } catch (error) {
      console.error('Error completing payment:', error);
      alert(`❌ Failed to complete payment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  if (pendingPayments.length === 0) {
    return null;
  }

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
      <h3 className="text-lg font-semibold text-yellow-800 mb-2">
        ⏳ Pending Payments Detected
      </h3>
      <p className="text-yellow-700 mb-3">
        You have {pendingPayments.length} pending payment(s). If you completed the payment in Xendit, 
        click "Complete Payment" to manually update the status.
      </p>
      
      {pendingPayments.map((payment) => (
        <div key={payment.id} className="bg-white rounded p-3 mb-2 border border-yellow-300">
          <div className="flex justify-between items-center">
            <div>
              <p className="font-medium">Amount: Rp {payment.amount.toLocaleString('id-ID')}</p>
              <p className="text-sm text-gray-600">
                Created: {new Date(payment.created_at).toLocaleString()}
              </p>
              {payment.external_id && (
                <p className="text-xs text-gray-500">ID: {payment.external_id}</p>
              )}
            </div>
            <button
              type="button"
              onClick={() => manuallyCompletePendingPayment(payment)}
              disabled={loading}
              className={`px-4 py-2 rounded text-white ${
                loading
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-green-500 hover:bg-green-600'
              }`}
            >
              {loading ? 'Processing...' : 'Complete Payment'}
            </button>
          </div>
        </div>
      ))}
      
      <div className="mt-3 pt-3 border-t border-yellow-300">
        <button
          type="button"
          onClick={checkPendingPayments}
          className="text-sm text-yellow-700 hover:text-yellow-900 underline"
        >
          🔄 Refresh Pending Payments
        </button>
      </div>
    </div>
  );
}

export default PaymentStatusChecker;
