import { useEffect, useState } from "react";
import { supabase } from "../supabase";

interface BalanceCardProps {
	onTopUpClick: () => void;
}

function BalanceCard({ onTopUpClick }: BalanceCardProps) {
	const [balance, setBalance] = useState<number>(0);
	const [loading, setLoading] = useState<boolean>(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		fetchBalance();
	}, []);

	const fetchBalance = async () => {
		try {
			setLoading(true);
			setError(null);

			const {
				data: { session },
			} = await supabase.auth.getSession();
			const userId = session?.user?.id;

			if (!userId) {
				setError("Not authenticated");
				setLoading(false);
				return;
			}

			// Get or create balance record using new schema
			let { data, error } = await supabase
				.from("balances")
				.select("user_balance")
				.eq("user_id", userId)
				.single();

			if (error && error.code === "PGRST116") {
				// No balance record exists, create one
				const { data: newBalance, error: insertError } = await supabase
					.from("balances")
					.insert([{ user_id: userId, user_balance: 0 }])
					.select("user_balance")
					.single();

				if (insertError) {
					console.error("Error creating balance:", insertError);
					setError("Failed to create balance record");
					return;
				}
				data = newBalance;
			} else if (error) {
				console.error("Error fetching balance:", error);
				setError("Failed to fetch balance");
				return;
			}

			setBalance(data?.user_balance || 0);
		} catch (err) {
			console.error("Unexpected error:", err);
			setError("An unexpected error occurred");
		} finally {
			setLoading(false);
		}
	};

	const formatCurrency = (amount: number): string => {
		return new Intl.NumberFormat("id-ID", {
			style: "currency",
			currency: "IDR",
			minimumFractionDigits: 0,
			maximumFractionDigits: 0,
		}).format(amount);
	};

	if (loading) {
		return (
			<div className="bg-white rounded-lg shadow-md p-6">
				<div className="animate-pulse">
					<div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
					<div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
					<div className="h-10 bg-gray-200 rounded w-full"></div>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="bg-white rounded-lg shadow-md p-6">
				<div className="text-red-600 mb-4">{error}</div>
				<button
					onClick={fetchBalance}
					className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
				>
					Retry
				</button>
			</div>
		);
	}

	return (
		<div className="bg-white rounded-lg shadow-md p-6">
			<h2 className="text-lg font-semibold text-gray-800 mb-2">Current Balance</h2>
			<p className="text-3xl font-bold text-green-600 mb-4">
				{formatCurrency(balance)}
			</p>
			<button
				onClick={onTopUpClick}
				className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
			>
				Top Up Balance
			</button>
		</div>
	);
}

export default BalanceCard;
