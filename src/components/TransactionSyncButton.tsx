import { useState } from 'react';
import { syncMissingTransactions, fixTransactionDescriptions } from '../utils/transactionSync';

interface TransactionSyncButtonProps {
  onSyncComplete?: () => void;
}

function TransactionSyncButton({ onSyncComplete }: TransactionSyncButtonProps) {
  const [loading, setLoading] = useState(false);
  const [lastResult, setLastResult] = useState<string | null>(null);

  const handleSyncMissing = async () => {
    setLoading(true);
    try {
      const result = await syncMissingTransactions();
      const message = `✅ Sync completed: ${result.created} created, ${result.updated} updated${result.errors.length > 0 ? `, ${result.errors.length} errors` : ''}`;
      setLastResult(message);
      
      if (result.created > 0 || result.updated > 0) {
        if (onSyncComplete) onSyncComplete();
        setTimeout(() => window.location.reload(), 1500);
      }
    } catch (error) {
      const message = `❌ Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      setLastResult(message);
    } finally {
      setLoading(false);
    }
  };

  const handleFixDescriptions = async () => {
    setLoading(true);
    try {
      const result = await fixTransactionDescriptions();
      const message = `✅ Descriptions fixed: ${result.updated} updated${result.errors.length > 0 ? `, ${result.errors.length} errors` : ''}`;
      setLastResult(message);
      
      if (result.updated > 0) {
        if (onSyncComplete) onSyncComplete();
        setTimeout(() => window.location.reload(), 1500);
      }
    } catch (error) {
      const message = `❌ Fix failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      setLastResult(message);
    } finally {
      setLoading(false);
    }
  };

  const handleFullSync = async () => {
    setLoading(true);
    try {
      // First sync missing transactions
      const syncResult = await syncMissingTransactions();
      
      // Then fix descriptions
      const fixResult = await fixTransactionDescriptions();
      
      const message = `✅ Full sync completed: ${syncResult.created} created, ${syncResult.updated + fixResult.updated} updated${(syncResult.errors.length + fixResult.errors.length) > 0 ? `, ${syncResult.errors.length + fixResult.errors.length} errors` : ''}`;
      setLastResult(message);
      
      if (syncResult.created > 0 || syncResult.updated > 0 || fixResult.updated > 0) {
        if (onSyncComplete) onSyncComplete();
        setTimeout(() => window.location.reload(), 2000);
      }
    } catch (error) {
      const message = `❌ Full sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      setLastResult(message);
    } finally {
      setLoading(false);
    }
  };

  // Only show in development mode
  if (!import.meta.env.DEV) {
    return null;
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <h3 className="text-lg font-semibold text-blue-800 mb-2">
        🔧 Transaction Sync Tools (Development)
      </h3>
      <p className="text-blue-700 mb-3 text-sm">
        Use these tools to fix missing transactions and update description formats.
      </p>
      
      <div className="flex flex-wrap gap-2 mb-3">
        <button
          type="button"
          onClick={handleSyncMissing}
          disabled={loading}
          className={`px-3 py-2 rounded text-sm ${
            loading 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-blue-500 hover:bg-blue-600'
          } text-white`}
        >
          {loading ? 'Processing...' : 'Sync Missing Transactions'}
        </button>
        
        <button
          type="button"
          onClick={handleFixDescriptions}
          disabled={loading}
          className={`px-3 py-2 rounded text-sm ${
            loading 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-green-500 hover:bg-green-600'
          } text-white`}
        >
          {loading ? 'Processing...' : 'Fix Descriptions'}
        </button>
        
        <button
          type="button"
          onClick={handleFullSync}
          disabled={loading}
          className={`px-3 py-2 rounded text-sm ${
            loading 
              ? 'bg-gray-400 cursor-not-allowed' 
              : 'bg-purple-500 hover:bg-purple-600'
          } text-white`}
        >
          {loading ? 'Processing...' : 'Full Sync (Both)'}
        </button>
      </div>
      
      {lastResult && (
        <div className={`text-sm p-2 rounded ${
          lastResult.startsWith('✅') 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {lastResult}
        </div>
      )}
      
      <div className="mt-3 pt-3 border-t border-blue-300 text-xs text-blue-600">
        <p><strong>Sync Missing:</strong> Creates transaction records for completed payments that don't have them</p>
        <p><strong>Fix Descriptions:</strong> Updates old format descriptions to new format</p>
        <p><strong>Full Sync:</strong> Runs both operations</p>
      </div>
    </div>
  );
}

export default TransactionSyncButton;
