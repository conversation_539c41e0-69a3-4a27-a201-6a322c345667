/** biome-ignore-all lint/nursery/useUniqueElementIds: <explanation> */
/** biome-ignore-all lint/a11y/noStaticElementInteractions: <explanation> */
/** biome-ignore-all lint/a11y/useKeyWithClickEvents: <explanation> */
import { useState, useEffect } from "react";
import ReactModal from "react-modal";
import { supabase } from "../supabase";
import { securePaymentService } from "../utils/securePaymentService";

// Set app element for React Modal accessibility
if (typeof document !== 'undefined') {
  ReactModal.setAppElement('#root');
}

interface TopUpModalProps {
	isOpen: boolean;
	onClose: () => void;
	onTopUp?: (amount: number) => void;
}

function TopUpModal({ isOpen, onClose, onTopUp }: TopUpModalProps) {
	const [amount, setAmount] = useState<string>("");
	const [loading, setLoading] = useState<boolean>(false);
	const [error, setError] = useState<string | null>(null);
	const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
	const [userEmail, setUserEmail] = useState<string | null>(null);

	// Check authentication status like DashboardHeader
	useEffect(() => {
		const checkAuth = async () => {
			try {
				const { data } = await supabase.auth.getSession();
				const session = data.session;
				const email = session?.user?.email ?? null;

				setUserEmail(email);
				setIsAuthenticated(!!session?.user);

				if (!session?.user) {
					console.log('❌ No authenticated user found in TopUpModal');
					setError("Please log in to continue");
				} else {
					console.log('✅ User authenticated in TopUpModal:', email);
					setError(null);
				}
			} catch (err) {
				console.error('Auth check error in TopUpModal:', err);
				setIsAuthenticated(false);
				setError("Authentication check failed");
			}
		};

		if (isOpen) {
			checkAuth();
		}
	}, [isOpen]);

	const handleQuickSelect = (selectedAmount: number) => {
		setAmount(selectedAmount.toString());
		setError(null);
	};

	const handleClose = () => {
		setAmount("");
		setError(null);
		setLoading(false);
		onClose();
	};

	const handleTopUpSubmit = async () => {
		// First check if user is authenticated
		if (!isAuthenticated) {
			setError("Please log in to continue");
			return;
		}

		if (!amount || Number(amount) <= 0) {
			setError("Please enter a valid amount");
			return;
		}

		const numAmount = Number(amount);
		if (numAmount < 10000) {
			setError("Minimum top-up amount is Rp 10,000");
			return;
		}

		if (numAmount > 10000000) {
			setError("Maximum top-up amount is Rp 10,000,000");
			return;
		}

		setLoading(true);
		setError(null);

		try {
			console.log('🚀 Starting top-up process...');

			// Double-check authentication before proceeding
			const { data: { session } } = await supabase.auth.getSession();
			console.log('📋 Session check:', {
				hasSession: !!session,
				hasUser: !!session?.user,
				userId: session?.user?.id,
				email: session?.user?.email,
				accessToken: session?.access_token ? 'present' : 'missing',
				expiresAt: session?.expires_at,
				currentTime: Math.floor(Date.now() / 1000),
				isExpired: session?.expires_at ? session.expires_at < Math.floor(Date.now() / 1000) : 'unknown'
			});

			if (!session?.user) {
				console.error('❌ No session found in TopUpModal');
				setError("Please log in to continue");
				setLoading(false);
				return;
			}

			if (!session.access_token) {
				console.error('❌ No access token in session');
				setError("Authentication token missing. Please log in again.");
				setLoading(false);
				return;
			}

			// Check if token is expired
			if (session.expires_at && session.expires_at < Math.floor(Date.now() / 1000)) {
				console.error('❌ Session token expired');
				setError("Session expired. Please log in again.");
				setLoading(false);
				return;
			}

			console.log('✅ Session valid, proceeding with payment...');

			// 🧪 EXPERIMENT: Log token payload dari frontend (sesuai saran mentor)
			try {
				const token = session.access_token;
				console.log("🔍 Session Token:", token);
				console.log("🔍 User ID:", session.user.id);

				// Decode token payload untuk debug
				const tokenParts = token.split('.');
				if (tokenParts.length === 3) {
					const base64 = tokenParts[1].replace(/-/g, '+').replace(/_/g, '/');
					const padded = base64 + '='.repeat((4 - base64.length % 4) % 4);
					const payload = JSON.parse(atob(padded));
					console.log("🔍 Payload (decoded):", {
						sub: payload.sub,
						email: payload.email,
						role: payload.role,
						aud: payload.aud,
						exp: payload.exp,
						provider: payload.app_metadata?.provider,
						isExpired: payload.exp < Math.floor(Date.now() / 1000),
						subMatchesUserId: payload.sub === session.user.id
					});

					// Validasi sesuai checklist mentor
					if (payload.sub !== session.user.id) {
						console.error('❌ MISMATCH: payload.sub !== session.user.id');
						setError("Token validation failed: User ID mismatch");
						setLoading(false);
						return;
					}

					if (payload.role === 'anon') {
						console.error('❌ ANON TOKEN: Using anon token instead of authenticated token');
						setError("Invalid token: Please log in again");
						setLoading(false);
						return;
					}

					if (payload.aud !== 'authenticated') {
						console.error('❌ INVALID AUDIENCE: Expected "authenticated", got:', payload.aud);
						setError("Invalid token audience");
						setLoading(false);
						return;
					}
				}
			} catch (tokenError) {
				console.error('❌ Token decode error:', tokenError);
				setError("Token validation failed");
				setLoading(false);
				return;
			}

			console.log('✅ Session valid, creating secure payment for user:', session.user.id, 'amount:', numAmount);

			// Validate n8n connection
			console.log('🔍 Checking n8n health...');
			const healthCheck = await securePaymentService.getWorkflowStatus();
			console.log('🔍 Health check result:', healthCheck);

			if (!healthCheck.success) {
				console.warn('⚠️ Health check failed, but continuing anyway for testing');
				// throw new Error('Payment service unavailable. Please try again later.');
			}

			// Create payment through secure n8n workflow
			console.log('🚀 Creating payment through n8n workflow...');
			console.log('🔍 Session data:', {
				userId: session.user.id,
				email: session.user.email,
				hasAccessToken: !!session.access_token
			});

			const paymentResult = await securePaymentService.createPayment({
				amount: numAmount,
				userId: session.user.id,
				userEmail: session.user.email || '',
				description: `Top-up balance - Rp ${numAmount.toLocaleString('id-ID')}`,
			});

			if (!paymentResult.success) {
				throw new Error(paymentResult.error || 'Payment creation failed');
			}

			console.log('✅ Payment created successfully through n8n:', paymentResult);

			// Call parent callback if provided
			if (onTopUp) {
				onTopUp(numAmount);
			}

			// Add delay for debugging
			console.log('⏳ Waiting 3 seconds before redirect for debugging...');
			await new Promise(resolve => setTimeout(resolve, 3000));

			// Redirect to Xendit payment page
			if (paymentResult.paymentUrl) {
				console.log('🔗 Redirecting to payment URL:', paymentResult.paymentUrl);
				window.location.href = paymentResult.paymentUrl;
			} else {
				throw new Error('Payment URL not received');
			}
		} catch (err) {
			console.error('Top-up error details:', err);

			// More specific error messages
			let errorMessage = 'Failed to process payment. Please try again.';

			if (err instanceof Error) {
				if (err.message.includes('Payment service unavailable')) {
					errorMessage = 'Payment service is currently unavailable. Please try again later.';
				} else if (err.message.includes('network') || err.message.includes('fetch')) {
					errorMessage = 'Network error. Please check your connection and try again.';
				} else if (err.message.includes('Unauthorized')) {
					errorMessage = 'Authentication error. Please log in again.';
				} else if (err.message.includes('n8n')) {
					errorMessage = 'Payment workflow error. Please contact support.';
				} else {
					errorMessage = err.message;
				}
			}

			setError(errorMessage);
		} finally {
			setLoading(false);
		}
	};

	return (
		<ReactModal
			isOpen={isOpen}
			onRequestClose={handleClose}
			contentLabel="Add Credit Modal"
			className="bg-white rounded shadow-lg"
			overlayClassName="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50"
		>
			<div className="border-b border-black px-6 pt-4 flex justify-between">
				<h2 className="text-xl font-bold mb-4">Top Up Balance</h2>
				<button
					type="button"
					onClick={handleClose}
					className="cursor-pointer bg-transparent border-none text-xl font-bold hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300 rounded p-1"
					aria-label="Close modal"
				>
					×
				</button>
			</div>
			<div className="px-6 py-4 flex flex-col gap-4">
				{error && (
					<div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
						{error}
					</div>
				)}
				<div className="flex flex-col gap-2">
					<label htmlFor="amount">amount(Rp)</label>
					<input
						className="border border-gray-300 w-[300px] rounded-lg p-2"
						type="number"
						id="amount"
						value={amount}
						onChange={(e) => setAmount(e.target.value)}
						placeholder="Enter amount (min. 10,000)"
						disabled={loading}
					/>
				</div>
				<div className="flex flex-row gap-4 mt-4 justify-between">
					<div
						className={`bg-white p-2 rounded-lg border border-gray-300 cursor-pointer hover:bg-gray-50 ${
							loading || !isAuthenticated ? 'opacity-50 cursor-not-allowed' : ''
						}`}
						onClick={() => !loading && isAuthenticated && handleQuickSelect(50000)}
					>
						50.000
					</div>
					<div
						className={`bg-white p-2 rounded-lg border border-gray-300 cursor-pointer hover:bg-gray-50 ${
							loading || !isAuthenticated ? 'opacity-50 cursor-not-allowed' : ''
						}`}
						onClick={() => !loading && isAuthenticated && handleQuickSelect(100000)}
					>
						100.000
					</div>
					<div
						className={`bg-white p-2 rounded-lg border border-gray-300 cursor-pointer hover:bg-gray-50 ${
							loading || !isAuthenticated ? 'opacity-50 cursor-not-allowed' : ''
						}`}
						onClick={() => !loading && isAuthenticated && handleQuickSelect(200000)}
					>
						200.000
					</div>
				</div>
				<div className="mt-4">
					<button
						type="button"
						className={`px-4 py-2 rounded-lg text-center w-full ${
							loading || !isAuthenticated
								? 'bg-gray-400 cursor-not-allowed'
								: 'bg-blue-500 hover:bg-blue-600'
						} text-white`}
						onClick={handleTopUpSubmit}
						disabled={loading || !isAuthenticated}
					>
						{loading ? 'Processing...' : !isAuthenticated ? 'Please Login First' : 'Top Up'}
					</button>
				</div>
			</div>
		</ReactModal>
	);
}

export default TopUpModal;
