import { supabase } from '../supabase';

export interface AuthValidationResult {
  isValid: boolean;
  userId?: string;
  email?: string;
  error?: string;
}

export interface N8nAuthHeaders {
  'Authorization': string;
  'X-User-ID': string;
  'Content-Type': string;
}

/**
 * Validate user authentication for n8n requests
 */
export class AuthValidator {
  private static instance: AuthValidator;

  public static getInstance(): AuthValidator {
    if (!AuthValidator.instance) {
      AuthValidator.instance = new AuthValidator();
    }
    return AuthValidator.instance;
  }

  /**
   * Validate current user session and return auth headers for n8n
   */
  async getAuthHeaders(): Promise<{
    success: boolean;
    headers?: N8nAuthHeaders;
    error?: string;
  }> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        return {
          success: false,
          error: `Session error: ${error.message}`,
        };
      }

      if (!session?.user) {
        return {
          success: false,
          error: 'No active user session',
        };
      }

      if (!session.access_token) {
        return {
          success: false,
          error: 'No access token available',
        };
      }

      // Validate token is not expired
      const tokenExp = session.expires_at;
      if (tokenExp && tokenExp * 1000 < Date.now()) {
        return {
          success: false,
          error: 'Session token expired',
        };
      }

      return {
        success: true,
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'X-User-ID': session.user.id,
          'Content-Type': 'application/json',
        },
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown auth error',
      };
    }
  }

  /**
   * Validate user session and return user info
   */
  async validateSession(): Promise<AuthValidationResult> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        return {
          isValid: false,
          error: `Session validation failed: ${error.message}`,
        };
      }

      if (!session?.user) {
        return {
          isValid: false,
          error: 'No active user session found',
        };
      }

      // Additional validation checks
      if (!session.user.email_confirmed_at) {
        return {
          isValid: false,
          error: 'Email not confirmed',
        };
      }

      return {
        isValid: true,
        userId: session.user.id,
        email: session.user.email || undefined,
      };

    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Session validation error',
      };
    }
  }

  /**
   * Refresh user session if needed
   */
  async refreshSessionIfNeeded(): Promise<{
    success: boolean;
    refreshed: boolean;
    error?: string;
  }> {
    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        return {
          success: false,
          refreshed: false,
          error: 'No session to refresh',
        };
      }

      // Check if token is close to expiry (within 5 minutes)
      const tokenExp = session.expires_at;
      const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000);

      if (tokenExp && tokenExp * 1000 < fiveMinutesFromNow) {
        console.log('Refreshing session token...');
        
        const { data, error } = await supabase.auth.refreshSession();

        if (error) {
          return {
            success: false,
            refreshed: false,
            error: `Session refresh failed: ${error.message}`,
          };
        }

        if (!data.session) {
          return {
            success: false,
            refreshed: false,
            error: 'Session refresh returned no session',
          };
        }

        return {
          success: true,
          refreshed: true,
        };
      }

      return {
        success: true,
        refreshed: false,
      };

    } catch (error) {
      return {
        success: false,
        refreshed: false,
        error: error instanceof Error ? error.message : 'Session refresh error',
      };
    }
  }

  /**
   * Validate user has permission for specific operation
   */
  async validatePermission(operation: 'payment' | 'balance' | 'transaction'): Promise<{
    hasPermission: boolean;
    error?: string;
  }> {
    try {
      const validation = await this.validateSession();

      if (!validation.isValid) {
        return {
          hasPermission: false,
          error: validation.error,
        };
      }

      // For now, all authenticated users have all permissions
      // In the future, you can implement role-based access control here
      switch (operation) {
        case 'payment':
        case 'balance':
        case 'transaction':
          return { hasPermission: true };
        default:
          return {
            hasPermission: false,
            error: `Unknown operation: ${operation}`,
          };
      }

    } catch (error) {
      return {
        hasPermission: false,
        error: error instanceof Error ? error.message : 'Permission validation error',
      };
    }
  }

  /**
   * Create secure request headers for API calls
   */
  async createSecureHeaders(additionalHeaders: Record<string, string> = {}): Promise<{
    success: boolean;
    headers?: Record<string, string>;
    error?: string;
  }> {
    try {
      const authResult = await this.getAuthHeaders();

      if (!authResult.success) {
        return {
          success: false,
          error: authResult.error,
        };
      }

      return {
        success: true,
        headers: {
          ...authResult.headers,
          ...additionalHeaders,
        },
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Header creation error',
      };
    }
  }

  /**
   * Validate user owns a specific resource
   */
  async validateResourceOwnership(resourceType: 'payment' | 'transaction', resourceId: string): Promise<{
    isOwner: boolean;
    error?: string;
  }> {
    try {
      const validation = await this.validateSession();

      if (!validation.isValid || !validation.userId) {
        return {
          isOwner: false,
          error: validation.error || 'Invalid session',
        };
      }

      let query;
      switch (resourceType) {
        case 'payment':
          query = supabase
            .from('payments')
            .select('user_id')
            .eq('id', resourceId)
            .single();
          break;
        case 'transaction':
          query = supabase
            .from('transactions')
            .select('user_id')
            .eq('id', resourceId)
            .single();
          break;
        default:
          return {
            isOwner: false,
            error: `Unknown resource type: ${resourceType}`,
          };
      }

      const { data, error } = await query;

      if (error) {
        return {
          isOwner: false,
          error: `Resource validation failed: ${error.message}`,
        };
      }

      if (!data) {
        return {
          isOwner: false,
          error: 'Resource not found',
        };
      }

      return {
        isOwner: data.user_id === validation.userId,
        error: data.user_id !== validation.userId ? 'Access denied: not resource owner' : undefined,
      };

    } catch (error) {
      return {
        isOwner: false,
        error: error instanceof Error ? error.message : 'Ownership validation error',
      };
    }
  }
}

// Export singleton instance
export const authValidator = AuthValidator.getInstance();

// Development utilities
if (import.meta.env.DEV) {
  (window as any).authValidator = authValidator;
  console.log('🔐 Auth Validator loaded. Available as window.authValidator');
}
