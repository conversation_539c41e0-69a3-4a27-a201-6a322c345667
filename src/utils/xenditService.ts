// DEPRECATED: Direct Xendit integration moved to n8n for security
// This file is kept for reference but should not be used in production

// WARNING: Direct Xendit client initialization removed for security
// All Xendit operations now go through n8n workflows

export interface CreateInvoiceParams {
  amount: number;
  userId: string;
  userEmail: string;
  description?: string;
}

export interface XenditInvoiceResponse {
  id: string;
  invoice_url: string;
  status: string;
  amount: number;
  external_id: string;
  payment_id: string; // Our UUID for the payment record
}

// DEPRECATED: This class is no longer secure and should not be used
// Use SecurePaymentService instead
export class PaymentService {
  private static instance: PaymentService;

  public static getInstance(): PaymentService {
    if (!PaymentService.instance) {
      PaymentService.instance = new PaymentService();
    }
    return PaymentService.instance;
  }

  /**
   * DEPRECATED: Direct Xendit invoice creation is insecure
   * Use SecurePaymentService instead which goes through n8n
   */
  async createInvoice(params: CreateInvoiceParams): Promise<XenditInvoiceResponse> {
    throw new Error('DEPRECATED: Direct Xendit API calls are not secure. Use SecurePaymentService instead.');
  }
}

// DEPRECATED: Export for backward compatibility only
export const paymentService = PaymentService.getInstance();

// Development warning
if (import.meta.env.DEV) {
  console.warn('⚠️  xenditService.ts is DEPRECATED and insecure. Use securePaymentService.ts instead.');
}
