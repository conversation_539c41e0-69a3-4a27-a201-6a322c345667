import { supabase } from '../supabase';
import { authValidator } from './authValidation';

export interface CreatePaymentParams {
  amount: number;
  userId: string;
  userEmail: string;
  description?: string;
}

export interface PaymentResponse {
  success: boolean;
  paymentUrl?: string;
  paymentId?: string;
  error?: string;
}

export class SecurePaymentService {
  private static instance: SecurePaymentService;
  private n8nWebhookUrl: string;

  constructor() {
    this.n8nWebhookUrl = import.meta.env.VITE_N8N_WEBHOOK_URL || 'http://localhost:5678/webhook';
  }

  public static getInstance(): SecurePaymentService {
    if (!SecurePaymentService.instance) {
      SecurePaymentService.instance = new SecurePaymentService();
    }
    return SecurePaymentService.instance;
  }

  /**
   * Create payment through n8n workflow (secure)
   */
  async createPayment(params: CreatePaymentParams): Promise<PaymentResponse> {
    try {
      // Debug: Check current session state
      const { data: { session } } = await supabase.auth.getSession();
      console.log('🔍 Current session state:', {
        hasSession: !!session,
        hasUser: !!session?.user,
        userId: session?.user?.id,
        email: session?.user?.email,
        accessToken: session?.access_token ? 'present' : 'missing',
        expiresAt: session?.expires_at,
        currentTime: Math.floor(Date.now() / 1000)
      });

      // Try to refresh session if it's close to expiry
      if (session?.expires_at) {
        const expiryTime = session.expires_at;
        const currentTime = Math.floor(Date.now() / 1000);
        const timeUntilExpiry = expiryTime - currentTime;

        console.log('🔍 Token expiry check:', {
          expiryTime,
          currentTime,
          timeUntilExpiry,
          willExpireSoon: timeUntilExpiry < 300 // 5 minutes
        });

        if (timeUntilExpiry < 300) { // Refresh if expires in less than 5 minutes
          console.log('🔄 Refreshing session...');
          const { error: refreshError } = await supabase.auth.refreshSession();
          if (refreshError) {
            console.error('❌ Session refresh failed:', refreshError);
          } else {
            console.log('✅ Session refreshed successfully');
          }
        }
      }

      // Validate user authentication and permissions
      const authValidation = await authValidator.validateSession();
      console.log('🔍 Auth validation result:', authValidation);

      if (!authValidation.isValid || authValidation.userId !== params.userId) {
        console.error('❌ Auth validation failed:', {
          isValid: authValidation.isValid,
          validationUserId: authValidation.userId,
          requestUserId: params.userId,
          error: authValidation.error
        });
        throw new Error('Unauthorized: Invalid user session');
      }

      const permissionCheck = await authValidator.validatePermission('payment');
      if (!permissionCheck.hasPermission) {
        throw new Error(`Access denied: ${permissionCheck.error}`);
      }

      // Refresh session if needed
      const refreshResult = await authValidator.refreshSessionIfNeeded();
      if (!refreshResult.success) {
        throw new Error(`Session refresh failed: ${refreshResult.error}`);
      }

      // Get secure headers for n8n request
      const headersResult = await authValidator.createSecureHeaders();
      if (!headersResult.success) {
        throw new Error(`Auth header creation failed: ${headersResult.error}`);
      }

      console.log('Creating payment through n8n workflow...');
      console.log('Headers being sent:', headersResult.headers);

      // DEBUG: Check the actual token being sent
      const authToken = headersResult.headers?.Authorization;
      if (authToken) {
        const token = authToken.replace('Bearer ', '');
        console.log('🔑 Token being sent to n8n:', {
          hasToken: !!token,
          tokenLength: token.length,
          tokenStart: token.substring(0, 50) + '...',
          isJWT: token.split('.').length === 3
        });

        // Try to decode the token to see what's inside
        try {
          const tokenParts = token.split('.');
          if (tokenParts.length === 3) {
            const base64 = tokenParts[1].replace(/-/g, '+').replace(/_/g, '/');
            const padded = base64 + '='.repeat((4 - base64.length % 4) % 4);
            const payload = JSON.parse(Buffer.from(padded, 'base64').toString());
            console.log('🔍 Token payload:', {
              sub: payload.sub,
              email: payload.email,
              exp: payload.exp,
              aud: payload.aud,
              iss: payload.iss,
              role: payload.role,
              isExpired: payload.exp < Math.floor(Date.now() / 1000)
            });
          }
        } catch (decodeError) {
          console.error('❌ Failed to decode token:', decodeError);
        }
      } else {
        console.error('❌ NO TOKEN IN HEADERS!');
      }

      console.log('Request URL:', `${this.n8nWebhookUrl}/create-payment`);
      console.log('Request body:', {
        requestType: 'create-payment',
        amount: params.amount,
        userId: params.userId,
        userEmail: params.userEmail,
        description: params.description || `Top-up balance - Rp ${params.amount.toLocaleString('id-ID')}`,
        timestamp: new Date().toISOString(),
      });

      // Test: Try health check first
      console.log('🧪 Testing n8n health check first...');
      try {
        const healthResponse = await fetch(`${this.n8nWebhookUrl}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        console.log('Health check status:', healthResponse.status);
        const healthText = await healthResponse.text();
        console.log('Health check response:', healthText);
      } catch (healthError) {
        console.error('Health check failed:', healthError);
      }

      // Call n8n webhook for payment creation
      console.log('🚀 Making payment request to n8n...');

      const requestHeaders = {
        'Content-Type': 'application/json',
        'Authorization': headersResult.headers?.Authorization || '',
        'X-User-ID': headersResult.headers?.['X-User-ID'] || params.userId,
      };

      // 🧪 EXPERIMENT: Log headers yang akan dikirim ke n8n
      console.log('📤 Headers being sent to n8n:', {
        hasAuthorization: !!requestHeaders.Authorization,
        authorizationStart: requestHeaders.Authorization ? requestHeaders.Authorization.substring(0, 20) + '...' : 'MISSING',
        hasUserID: !!requestHeaders['X-User-ID'],
        userID: requestHeaders['X-User-ID']
      });

      const response = await fetch(`${this.n8nWebhookUrl}/create-payment`, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify({
          requestType: 'create-payment',
          amount: params.amount,
          userId: params.userId,
          userEmail: params.userEmail,
          description: params.description || `Top-up balance - Rp ${params.amount.toLocaleString('id-ID')}`,
          timestamp: new Date().toISOString(),
        }),
      });

      console.log('n8n Response Status:', response.status, response.statusText);
      console.log('n8n Response Headers:', Object.fromEntries(response.headers.entries()));

      // Get response text first to debug
      const responseText = await response.text();
      console.log('n8n Response Text:', responseText);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText} - Response: ${responseText}`);
      }

      // Try to parse JSON
      let result: { success: boolean; paymentUrl?: string; paymentId?: string; error?: string; message?: string; redirectToLogin?: boolean };
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON Parse Error:', parseError);
        console.error('Response was:', responseText);
        throw new Error(`Invalid JSON response from n8n: ${responseText}`);
      }

      if (!result.success) {
        console.error('❌ Payment creation failed:', result);

        // Handle authentication errors that require login redirect
        if (result.redirectToLogin) {
          console.log('🔄 Redirecting to login due to auth failure');
          // Redirect to login page
          window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}`;
          return {
            success: false,
            error: result.message || 'Authentication required',
          };
        }
        throw new Error(result.error || result.message || 'Payment creation failed');
      }

      console.log('Payment created successfully through n8n:', result);

      return {
        success: true,
        paymentUrl: result.paymentUrl,
        paymentId: result.paymentId,
      };

    } catch (error) {
      console.error('Secure payment creation failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Check payment status through n8n
   */
  async checkPaymentStatus(paymentId: string): Promise<{
    success: boolean;
    status?: string;
    error?: string;
  }> {
    try {
      // Validate user authentication
      const authValidation = await authValidator.validateSession();
      if (!authValidation.isValid || !authValidation.userId) {
        throw new Error('Unauthorized: Invalid user session');
      }

      // Validate user owns this payment
      const ownershipCheck = await authValidator.validateResourceOwnership('payment', paymentId);
      if (!ownershipCheck.isOwner) {
        throw new Error(`Access denied: ${ownershipCheck.error}`);
      }

      // Get secure headers
      const headersResult = await authValidator.createSecureHeaders();
      if (!headersResult.success) {
        throw new Error(`Auth header creation failed: ${headersResult.error}`);
      }

      const response = await fetch(`${this.n8nWebhookUrl}/check-payment-status`, {
        method: 'POST',
        headers: headersResult.headers,
        body: JSON.stringify({
          paymentId,
          userId: authValidation.userId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result;

    } catch (error) {
      console.error('Payment status check failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }



  /**
   * Get n8n workflow status (for debugging)
   */
  async getWorkflowStatus(): Promise<{
    success: boolean;
    status?: string;
    error?: string;
  }> {
    try {
      console.log('🔍 Health check URL:', `${this.n8nWebhookUrl}`);
      const response = await fetch(`${this.n8nWebhookUrl}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`n8n not reachable: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: true,
        status: result.status || 'active',
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'n8n connection failed',
      };
    }
  }
}

// Export singleton instance
export const securePaymentService = SecurePaymentService.getInstance();

// Development utilities
if (import.meta.env.DEV) {
  (window as unknown as { securePaymentService: SecurePaymentService }).securePaymentService = securePaymentService;
  console.log('🔒 Secure Payment Service loaded. Available as window.securePaymentService');
}
