import { supabase } from '../supabase';

/**
 * Sync missing transactions from completed payments
 */
export const syncMissingTransactions = async (): Promise<{
  created: number;
  updated: number;
  errors: string[];
}> => {
  const result = {
    created: 0,
    updated: 0,
    errors: [] as string[]
  };

  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    console.log('🔄 Starting transaction sync for user:', session.user.id);

    // Get all completed payments
    const { data: completedPayments, error: paymentsError } = await supabase
      .from('payments')
      .select('*')
      .eq('user_id', session.user.id)
      .eq('status', 'completed')
      .order('created_at', { ascending: false });

    if (paymentsError) {
      throw new Error(`Failed to fetch payments: ${paymentsError.message}`);
    }

    if (!completedPayments || completedPayments.length === 0) {
      console.log('ℹ️ No completed payments found');
      return result;
    }

    console.log(`📊 Found ${completedPayments.length} completed payments`);

    // Get existing transactions
    const { data: existingTransactions, error: transactionsError } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', session.user.id)
      .eq('type', 'purchase');

    if (transactionsError) {
      throw new Error(`Failed to fetch transactions: ${transactionsError.message}`);
    }

    console.log(`📊 Found ${existingTransactions?.length || 0} existing purchase transactions`);

    // Process each completed payment
    for (const payment of completedPayments) {
      try {
        // Check if transaction already exists for this payment
        const existingTransaction = existingTransactions?.find(t => 
          t.amount === payment.amount && 
          t.date === payment.date &&
          t.type === 'purchase'
        );

        if (existingTransaction) {
          // Check if description needs updating
          const expectedDescription = `Credit purchase: ${payment.amount} credits`;
          if (existingTransaction.description !== expectedDescription) {
            console.log(`🔄 Updating transaction description for payment ${payment.id}`);
            
            const { error: updateError } = await supabase
              .from('transactions')
              .update({
                description: expectedDescription
              })
              .eq('id', existingTransaction.id);

            if (updateError) {
              result.errors.push(`Failed to update transaction ${existingTransaction.id}: ${updateError.message}`);
            } else {
              result.updated++;
              console.log(`✅ Updated transaction description for payment ${payment.id}`);
            }
          }
        } else {
          // Create missing transaction
          console.log(`➕ Creating missing transaction for payment ${payment.id}`);
          
          const { error: createError } = await supabase
            .from('transactions')
            .insert({
              user_id: session.user.id,
              type: 'purchase',
              amount: payment.amount,
              description: `Credit purchase: ${payment.amount} credits`,
              date: payment.date,
              created_at: payment.paid_at || payment.created_at,
            });

          if (createError) {
            result.errors.push(`Failed to create transaction for payment ${payment.id}: ${createError.message}`);
          } else {
            result.created++;
            console.log(`✅ Created transaction for payment ${payment.id}`);
          }
        }
      } catch (error) {
        const errorMsg = `Error processing payment ${payment.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        result.errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    console.log(`🎉 Transaction sync completed:`, result);
    return result;

  } catch (error) {
    const errorMsg = `Transaction sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    result.errors.push(errorMsg);
    console.error(errorMsg);
    return result;
  }
};

/**
 * Fix all transaction descriptions to use the new format
 */
export const fixTransactionDescriptions = async (): Promise<{
  updated: number;
  errors: string[];
}> => {
  const result = {
    updated: 0,
    errors: [] as string[]
  };

  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    console.log('🔄 Fixing transaction descriptions for user:', session.user.id);

    // Get all purchase transactions with old format descriptions
    const { data: transactions, error: fetchError } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', session.user.id)
      .eq('type', 'purchase')
      .not('description', 'like', 'Credit purchase:%');

    if (fetchError) {
      throw new Error(`Failed to fetch transactions: ${fetchError.message}`);
    }

    if (!transactions || transactions.length === 0) {
      console.log('ℹ️ No transactions need description updates');
      return result;
    }

    console.log(`📊 Found ${transactions.length} transactions to update`);

    // Update each transaction
    for (const transaction of transactions) {
      try {
        const newDescription = `Credit purchase: ${transaction.amount} credits`;
        
        const { error: updateError } = await supabase
          .from('transactions')
          .update({
            description: newDescription
          })
          .eq('id', transaction.id);

        if (updateError) {
          result.errors.push(`Failed to update transaction ${transaction.id}: ${updateError.message}`);
        } else {
          result.updated++;
          console.log(`✅ Updated description for transaction ${transaction.id}`);
        }
      } catch (error) {
        const errorMsg = `Error updating transaction ${transaction.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        result.errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    console.log(`🎉 Description fix completed: ${result.updated} updated, ${result.errors.length} errors`);
    return result;

  } catch (error) {
    const errorMsg = `Description fix failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    result.errors.push(errorMsg);
    console.error(errorMsg);
    return result;
  }
};

// Add to window for easy testing
if (typeof window !== 'undefined') {
  (window as any).syncMissingTransactions = syncMissingTransactions;
  (window as any).fixTransactionDescriptions = fixTransactionDescriptions;
}

console.log('🔧 Transaction sync utilities loaded. Use syncMissingTransactions() or fixTransactionDescriptions() in console.');
