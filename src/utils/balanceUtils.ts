import { supabase } from '../supabase';

/**
 * Safely update user balance by handling existing records properly
 */
export const updateUserBalance = async (userId: string, amountToAdd: number): Promise<number> => {
  try {
    // First, try to get the current balance
    const { data: existingBalance, error: selectError } = await supabase
      .from('balances')
      .select('current_balance')
      .eq('user_id', userId)
      .single();

    let newBalance: number;

    if (selectError && selectError.code === 'PGRST116') {
      // No existing balance record, create one
      newBalance = amountToAdd;
      
      const { error: insertError } = await supabase
        .from('balances')
        .insert({
          user_id: userId,
          current_balance: newBalance,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (insertError) {
        throw new Error(`Failed to create balance record: ${insertError.message}`);
      }
    } else if (selectError) {
      // Some other error occurred
      throw new Error(`Failed to fetch balance: ${selectError.message}`);
    } else {
      // Balance record exists, update it
      newBalance = (existingBalance?.current_balance || 0) + amountToAdd;
      
      const { error: updateError } = await supabase
        .from('balances')
        .update({
          current_balance: newBalance,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', userId);

      if (updateError) {
        throw new Error(`Failed to update balance: ${updateError.message}`);
      }
    }

    console.log(`✅ Balance updated for user ${userId}: +${amountToAdd} = ${newBalance}`);
    return newBalance;
  } catch (error) {
    console.error('❌ Error updating balance:', error);
    throw error;
  }
};

/**
 * Create a transaction record with proper formatting
 */
export const createTransactionRecord = async (
  userId: string,
  amount: number,
  description: string,
  type: 'credit' | 'debit' | 'purchase' | 'refund' = 'credit'
): Promise<void> => {
  try {
    const { error } = await supabase
      .from('transactions')
      .insert({
        user_id: userId,
        type: type,
        amount: amount,
        description: description, // Use the description as provided, don't modify it
        date: new Date().toISOString().split('T')[0], // DATE format
        created_at: new Date().toISOString(),
      });

    if (error) {
      throw new Error(`Failed to create transaction: ${error.message}`);
    }

    console.log(`✅ Transaction created: ${type} ${amount} for user ${userId}`);
  } catch (error) {
    console.error('❌ Error creating transaction:', error);
    throw error;
  }
};

/**
 * Complete a payment by updating payment status, balance, and creating transaction
 */
export const completePayment = async (
  paymentId: string, 
  userId: string, 
  amount: number,
  invoiceId?: string
): Promise<void> => {
  try {
    console.log(`🔄 Completing payment ${paymentId} for user ${userId}, amount: ${amount}`);

    // 1. Update payment status
    const { error: paymentError } = await supabase
      .from('payments')
      .update({
        status: 'completed',
        paid_at: new Date().toISOString(),
      })
      .eq('id', paymentId);

    if (paymentError) {
      throw new Error(`Failed to update payment: ${paymentError.message}`);
    }

    // 2. Update user balance
    const newBalance = await updateUserBalance(userId, amount);

    // 3. Create transaction record with proper format
    await createTransactionRecord(
      userId,
      amount,
      `Credit purchase: ${amount} credits`,
      'purchase' // Top-up menggunakan type 'purchase'
    );

    console.log(`✅ Payment ${paymentId} completed successfully. New balance: ${newBalance}`);
  } catch (error) {
    console.error(`❌ Error completing payment ${paymentId}:`, error);
    throw error;
  }
};
