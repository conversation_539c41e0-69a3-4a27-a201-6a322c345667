// DEPRECATED: Direct Xendit testing moved to n8n workflows
// This file is kept for reference but should not be used in production

export const testXenditConnection = async () => {
  console.warn('⚠️ testXenditConnection is DEPRECATED. Xendit integration now goes through n8n workflows.');
  console.log('🔒 Use securePaymentService.getWorkflowStatus() to test n8n connection instead.');

  // Check if n8n environment variables are configured
  const n8nWebhookUrl = import.meta.env.VITE_N8N_WEBHOOK_URL;
  const n8nApiUrl = import.meta.env.VITE_N8N_API_URL;

  if (!n8nWebhookUrl) {
    console.error('❌ VITE_N8N_WEBHOOK_URL not found in environment variables');
    return false;
  }

  if (!n8nApiUrl) {
    console.error('❌ VITE_N8N_API_URL not found in environment variables');
    return false;
  }

  console.log('✅ n8n environment variables found');
  console.log('🔗 n8n Webhook URL:', n8nWebhookUrl);
  console.log('🔗 n8n API URL:', n8nApiUrl);

  // Test n8n health endpoint
  try {
    const response = await fetch(`${n8nWebhookUrl}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('❌ n8n health check failed:', response.status, response.statusText);
      return false;
    }

    const result = await response.json();
    console.log('✅ n8n connection successful:', result);
    return true;

  } catch (error) {
    console.error('❌ Failed to connect to n8n:', error);
    console.log('💡 Make sure n8n is running and accessible at:', n8nWebhookUrl);
    return false;
  }
};

// Test n8n workflow status
export const testN8nWorkflows = async () => {
  console.log('🧪 Testing n8n Workflows...');

  const { securePaymentService } = await import('./securePaymentService');

  try {
    const healthCheck = await securePaymentService.getWorkflowStatus();

    if (healthCheck.success) {
      console.log('✅ n8n workflows are accessible:', healthCheck.status);
      return true;
    } else {
      console.error('❌ n8n workflow health check failed:', healthCheck.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Failed to test n8n workflows:', error);
    return false;
  }
};

// Helper functions for browser console
if (import.meta.env.DEV) {
  (window as unknown as {
    testXendit: typeof testXenditConnection;
    testN8nWorkflows: typeof testN8nWorkflows;
  }).testXendit = testXenditConnection;

  (window as unknown as {
    testXendit: typeof testXenditConnection;
    testN8nWorkflows: typeof testN8nWorkflows;
  }).testN8nWorkflows = testN8nWorkflows;

  console.log('🔧 Test utilities loaded:');
  console.log('  - testXendit() - Test n8n connection (deprecated Xendit direct test)');
  console.log('  - testN8nWorkflows() - Test n8n workflow status');
}
