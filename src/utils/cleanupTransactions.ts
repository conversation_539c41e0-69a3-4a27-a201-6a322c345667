import { supabase } from '../supabase';

/**
 * Clean up duplicate transactions and fix balance
 */
export const cleanupDuplicateTransactions = async (): Promise<{
  removed: number;
  balanceFixed: boolean;
  errors: string[];
}> => {
  const result = {
    removed: 0,
    balanceFixed: false,
    errors: [] as string[]
  };

  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    console.log('🧹 Starting cleanup for user:', session.user.id);

    // Get all purchase transactions
    const { data: transactions, error: fetchError } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', session.user.id)
      .eq('type', 'purchase')
      .order('created_at', { ascending: true });

    if (fetchError) {
      throw new Error(`Failed to fetch transactions: ${fetchError.message}`);
    }

    if (!transactions || transactions.length === 0) {
      console.log('ℹ️ No transactions found');
      return result;
    }

    console.log(`📊 Found ${transactions.length} purchase transactions`);

    // Group transactions by amount and date to find duplicates
    const transactionGroups: { [key: string]: any[] } = {};
    
    transactions.forEach(transaction => {
      const key = `${transaction.amount}_${transaction.date}`;
      if (!transactionGroups[key]) {
        transactionGroups[key] = [];
      }
      transactionGroups[key].push(transaction);
    });

    // Remove duplicates (keep the one with correct description format)
    for (const [key, group] of Object.entries(transactionGroups)) {
      if (group.length > 1) {
        console.log(`🔍 Found ${group.length} duplicates for ${key}`);
        
        // Sort by preference: new format first, then by creation date
        group.sort((a, b) => {
          // Prefer new format "Credit purchase: X credits"
          const aIsNewFormat = a.description.startsWith('Credit purchase:');
          const bIsNewFormat = b.description.startsWith('Credit purchase:');
          
          if (aIsNewFormat && !bIsNewFormat) return -1;
          if (!aIsNewFormat && bIsNewFormat) return 1;
          
          // If both same format, prefer newer
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });

        // Keep the first one (best), remove the rest
        const toKeep = group[0];
        const toRemove = group.slice(1);

        console.log(`✅ Keeping transaction: ${toKeep.description}`);
        
        for (const transaction of toRemove) {
          console.log(`🗑️ Removing duplicate: ${transaction.description}`);
          
          const { error: deleteError } = await supabase
            .from('transactions')
            .delete()
            .eq('id', transaction.id);

          if (deleteError) {
            result.errors.push(`Failed to delete transaction ${transaction.id}: ${deleteError.message}`);
          } else {
            result.removed++;
          }
        }
      }
    }

    console.log(`🎉 Cleanup completed: ${result.removed} duplicates removed`);
    return result;

  } catch (error) {
    const errorMsg = `Cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    result.errors.push(errorMsg);
    console.error(errorMsg);
    return result;
  }
};

/**
 * Recalculate and fix user balance based on transactions
 */
export const recalculateBalance = async (): Promise<{
  oldBalance: number;
  newBalance: number;
  fixed: boolean;
  errors: string[];
}> => {
  const result = {
    oldBalance: 0,
    newBalance: 0,
    fixed: false,
    errors: [] as string[]
  };

  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) {
      throw new Error('User not authenticated');
    }

    console.log('🧮 Recalculating balance for user:', session.user.id);

    // Get current balance
    const { data: currentBalance, error: balanceError } = await supabase
      .from('balances')
      .select('current_balance')
      .eq('user_id', session.user.id)
      .single();

    if (balanceError && balanceError.code !== 'PGRST116') {
      throw new Error(`Failed to fetch current balance: ${balanceError.message}`);
    }

    result.oldBalance = currentBalance?.current_balance || 0;

    // Calculate balance from all transactions
    const { data: transactions, error: transactionsError } = await supabase
      .from('transactions')
      .select('type, amount')
      .eq('user_id', session.user.id);

    if (transactionsError) {
      throw new Error(`Failed to fetch transactions: ${transactionsError.message}`);
    }

    // Calculate correct balance
    let calculatedBalance = 0;
    
    if (transactions) {
      for (const transaction of transactions) {
        if (transaction.type === 'purchase' || transaction.type === 'credit') {
          // Add for purchases/credits (top-ups)
          calculatedBalance += transaction.amount;
        } else if (transaction.type === 'debit') {
          // Subtract for debits (usage)
          calculatedBalance -= transaction.amount;
        }
        // Note: refunds would add back
      }
    }

    result.newBalance = calculatedBalance;

    console.log(`💰 Balance calculation:`);
    console.log(`   Current: ${result.oldBalance}`);
    console.log(`   Calculated: ${result.newBalance}`);

    // Update balance if different
    if (Math.abs(result.oldBalance - result.newBalance) > 0.01) {
      console.log('🔄 Updating balance...');
      
      const { error: updateError } = await supabase
        .from('balances')
        .upsert({
          user_id: session.user.id,
          current_balance: result.newBalance,
          updated_at: new Date().toISOString(),
        });

      if (updateError) {
        result.errors.push(`Failed to update balance: ${updateError.message}`);
      } else {
        result.fixed = true;
        console.log('✅ Balance updated successfully');
      }
    } else {
      console.log('✅ Balance is already correct');
    }

    return result;

  } catch (error) {
    const errorMsg = `Balance recalculation failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    result.errors.push(errorMsg);
    console.error(errorMsg);
    return result;
  }
};

/**
 * Full cleanup: remove duplicates and fix balance
 */
export const fullCleanup = async () => {
  console.log('🚀 Starting full cleanup...');
  
  // Step 1: Clean duplicates
  const cleanupResult = await cleanupDuplicateTransactions();
  
  // Step 2: Recalculate balance
  const balanceResult = await recalculateBalance();
  
  const summary = {
    duplicatesRemoved: cleanupResult.removed,
    balanceFixed: balanceResult.fixed,
    oldBalance: balanceResult.oldBalance,
    newBalance: balanceResult.newBalance,
    errors: [...cleanupResult.errors, ...balanceResult.errors]
  };
  
  console.log('🎉 Full cleanup completed:', summary);
  return summary;
};

// Add to window for easy testing
if (typeof window !== 'undefined') {
  (window as any).cleanupDuplicateTransactions = cleanupDuplicateTransactions;
  (window as any).recalculateBalance = recalculateBalance;
  (window as any).fullCleanup = fullCleanup;
}

console.log('🧹 Cleanup utilities loaded. Use fullCleanup() in console.');
