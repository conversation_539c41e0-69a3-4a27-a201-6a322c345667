// Manual webhook trigger for testing sandbox payments
// This simulates what Xendit webhook would send

export const triggerManualWebhook = async (invoiceId: string, amount: number, externalId: string) => {
  console.log('🔧 Triggering manual webhook for testing...');
  
  const webhookPayload = {
    id: invoiceId,
    status: 'PAID',
    external_id: externalId,
    amount: amount,
    paid_at: new Date().toISOString(),
    payment_method: 'BANK_TRANSFER',
    currency: 'IDR'
  };

  try {
    // Call our webhook endpoint directly for testing
    const response = await fetch('/.netlify/functions/xendit-webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-callback-token': import.meta.env.XENDIT_WEBHOOK_TOKEN || 'test-token'
      },
      body: JSON.stringify(webhookPayload)
    });

    const result = await response.json();
    console.log('✅ Manual webhook response:', result);
    
    if (response.ok) {
      console.log('✅ Payment status should now be updated to completed');
      // Refresh the page to show updated data
      window.location.reload();
    } else {
      console.error('❌ Webhook failed:', result);
    }
    
    return result;
  } catch (error) {
    console.error('❌ Manual webhook trigger failed:', error);
    throw error;
  }
};

// Add to window for easy testing
(window as any).triggerManualWebhook = triggerManualWebhook;

console.log('🔧 Manual webhook trigger loaded. Use triggerManualWebhook(invoiceId, amount, externalId) in console.');
