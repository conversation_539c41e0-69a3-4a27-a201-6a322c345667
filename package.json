{"name": "sumopod", "version": "0.1.0", "private": true, "dependencies": {"@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.50.2", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-modal": "^3.16.3", "react-router-dom": "^6.30.1", "typescript": "^5.8.3", "web-vitals": "^2.1.4"}, "scripts": {"dev": "vite", "start": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "prepare": "husky"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@biomejs/biome": "2.0.5", "@testing-library/jest-dom": "^6.6.3", "@types/node": "^24.0.4", "@types/react-modal": "^3.16.3", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^15.5.2", "postcss": "^8.5.6", "tailwindcss": "^3.0.0", "vite": "^6.3.5", "vitest": "^3.2.4"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}}