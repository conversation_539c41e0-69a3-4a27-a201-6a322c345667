# Supabase Configuration
VITE_SUPABASE_URL="your_supabase_project_url"
VITE_SUPABASE_ANON_KEY="your_supabase_anon_key"

# Xendit Configuration (Frontend - Public Key Only)
VITE_XENDIT_PUBLIC_KEY="xnd_public_development_your_public_key_here"

# n8n Configuration
VITE_N8N_WEBHOOK_URL="http://localhost:5678/webhook"
VITE_N8N_API_URL="http://localhost:5678/api/v1"

# Server-side only (no VITE_ prefix) - Store these in n8n or backend
XENDIT_SECRET_KEY="xnd_development_your_secret_key_here"
XENDIT_WEBHOOK_TOKEN="your_webhook_verification_token_here"
N8N_API_KEY="your_n8n_api_key_here"

# Supabase Service Role Key (for server-side operations)
SUPABASE_SERVICE_ROLE_KEY="your_supabase_service_role_key_here"

# Instructions:
# 1. Copy this file to .env
# 2. Replace all "your_*_here" values with actual keys from:
#    - Supabase: https://app.supabase.com/project/[your-project]/settings/api
#    - Xendit: https://dashboard.xendit.co/settings/developers#api-keys
# 3. Make sure to use DEVELOPMENT keys for testing
# 4. Never commit .env file to git (it's in .gitignore)
