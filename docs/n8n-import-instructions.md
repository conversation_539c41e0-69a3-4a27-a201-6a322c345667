# n8n Workflow Import Instructions

Complete step-by-step guide to import and configure the secure payment workflows in your n8n instance.

## 🚀 Quick Start Checklist

- [ ] Create credentials in n8n
- [ ] Import 3 workflow JSON files
- [ ] Update credential references
- [ ] Update Supabase URLs and keys
- [ ] Activate all workflows
- [ ] Test endpoints

## Step 1: Create Credentials

Go to `https://n8n-vaw3sogm.runner.web.id/credentials` and create these credentials:

### **Credential 1: Xendit API**
- **Type**: `HTTP Basic Auth`
- **Name**: `Xendit API`
- **User**: `xnd_development_XzOz51cv5CryV5bQWytvZQZFhjFFKZnqc9MygvOnBait0QvQ1UvOlSREu9TKq`
- **Password**: (leave empty)

### **Credential 2: Supabase Service**
- **Type**: `HTTP Header Auth`
- **Name**: `Supabase Service`
- **Header Name**: `Authorization`
- **Header Value**: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your_actual_service_role_key_here`

## Step 2: Import Workflows

### **Import Workflow 1: Payment Invoice Creation**
1. Go to `https://n8n-vaw3sogm.runner.web.id/workflows`
2. Click **"+"** → **"Import from File"**
3. Upload `docs/n8n-payment-invoice-workflow.json`
4. **Don't activate yet** - need to update credentials first

### **Import Workflow 2: Payment Webhook Processing**
1. Click **"+"** → **"Import from File"**
2. Upload `docs/n8n-webhook-processing-workflow.json`
3. **Don't activate yet**

### **Import Workflow 3: Health Check**
1. Click **"+"** → **"Import from File"**
2. Upload `docs/n8n-health-check-workflow.json`
3. This one can be activated immediately (no credentials needed)

## Step 3: Configure Payment Invoice Creation Workflow

### **Edit the Workflow**:
1. Open **"Payment Invoice Creation"** workflow
2. Click **"Create Xendit Invoice"** node
3. **Authentication** → Select **"Xendit API"** credential
4. Click **"Store Payment Record"** node
5. **Authentication** → Select **"Supabase Service"** credential
6. **Update Supabase URL**:
   - Change `https://wnqjqjqjqjqjqjqjqjqj.supabase.co` to your actual Supabase URL
7. **Update API Key**:
   - In headers, replace `your_anon_key_here` with your actual Supabase anon key

### **Required Updates**:
```json
// In "Store Payment Record" node headers:
{
  "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your_actual_anon_key",
  "Content-Type": "application/json",
  "Prefer": "return=representation"
}
```

## Step 4: Configure Payment Webhook Processing Workflow

### **Edit the Workflow**:
1. Open **"Payment Webhook Processing"** workflow
2. Click **"Complete Payment"** node
3. **Authentication** → Select **"Supabase Service"** credential
4. **Update Supabase URL**:
   - Change `https://wnqjqjqjqjqjqjqjqjqj.supabase.co` to your actual Supabase URL
5. **Update API Key**:
   - In headers, replace `your_anon_key_here` with your actual Supabase anon key

## Step 5: Get Your Supabase Keys

### **Find Your Supabase URL and Keys**:
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to **Settings** → **API**
4. Copy:
   - **Project URL** (e.g., `https://abcdefgh.supabase.co`)
   - **anon public** key (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)
   - **service_role** key (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9`)

### **Update Credential**:
1. Go back to **Credentials** → **"Supabase Service"**
2. Update **Header Value** with: `Bearer your_actual_service_role_key`

## Step 6: Activate Workflows

### **Activate Each Workflow**:
1. **Payment Invoice Creation** → Click **"Active"** toggle
2. **Payment Webhook Processing** → Click **"Active"** toggle  
3. **Health Check** → Click **"Active"** toggle

### **Verify Active Status**:
All workflows should show **"Active"** status in the workflows list.

## Step 7: Test the Setup

### **Test 1: Health Check**
```bash
curl https://n8n-vaw3sogm.runner.web.id/webhook/health
```

**Expected Response**:
```json
{
  "status": "active",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "n8n-payment-processor",
  "endpoints": {
    "create-payment": "https://n8n-vaw3sogm.runner.web.id/webhook/create-payment",
    "xendit-payment": "https://n8n-vaw3sogm.runner.web.id/webhook/xendit-payment",
    "health": "https://n8n-vaw3sogm.runner.web.id/webhook/health"
  }
}
```

### **Test 2: Frontend Integration**
1. Start your React app: `npm run dev`
2. Open browser console
3. Run: `testN8nWorkflows()`
4. Should see: `✅ n8n connection successful`

### **Test 3: Payment Creation (Manual)**
1. Login to your app
2. Try to create a top-up payment
3. Check n8n **Executions** tab for logs
4. Verify no errors in execution

## Step 8: Configure Xendit Webhook

### **Update Xendit Dashboard**:
1. Login to [Xendit Dashboard](https://dashboard.xendit.co)
2. Go to **Settings** → **Webhooks**
3. **Add Webhook URL**: `https://n8n-vaw3sogm.runner.web.id/webhook/xendit-payment`
4. **Select Events**: `invoice.paid`
5. **Verification Token**: `sGqcEXjShdnLd4S6DITIbOxbbKWxsPK0018WQb8JqtoHxlo9`

## 🔍 Troubleshooting

### **Common Issues**:

#### **Issue**: "Credential not found"
**Solution**: Make sure credential names match exactly:
- `Xendit API` (HTTP Basic Auth)
- `Supabase Service` (HTTP Header Auth)

#### **Issue**: "Supabase connection failed"
**Solution**: 
- Verify Supabase URL is correct
- Check service_role key is valid
- Ensure anon key is updated in headers

#### **Issue**: "Webhook token invalid"
**Solution**: 
- Check webhook token in "Validate Webhook" function matches Xendit settings
- Token: `sGqcEXjShdnLd4S6DITIbOxbbKWxsPK0018WQb8JqtoHxlo9`

#### **Issue**: "Authentication failed"
**Solution**:
- Verify JWT token is being sent from frontend
- Check token format: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- Ensure user is logged in to Supabase

### **Debug Steps**:
1. **Check n8n Executions**: Go to **Executions** tab to see detailed logs
2. **Test Individual Nodes**: Use the "Test" button on each node
3. **Check Network Tab**: Verify requests are reaching n8n endpoints
4. **Verify Credentials**: Test credentials individually

## ✅ Success Criteria

You'll know everything is working when:
- [ ] Health check returns 200 OK
- [ ] Frontend top-up creates payment without errors
- [ ] Payment completion updates user balance
- [ ] No secret keys visible in browser DevTools
- [ ] All requests go through n8n (not directly to Xendit)
- [ ] n8n execution logs show successful runs

## 📞 Need Help?

If you encounter issues:
1. **Check n8n Executions** for detailed error messages
2. **Verify all credentials** are correctly configured
3. **Test each workflow individually** using n8n's test feature
4. **Check browser Network tab** to see if requests reach n8n
5. **Verify Supabase functions** are created (from `docs/supabase-functions.sql`)

## 🎯 Final Notes

- **Security**: No secret keys are exposed in frontend anymore
- **Authentication**: All requests are validated before processing
- **Error Handling**: Proper error responses with login redirects
- **Monitoring**: All payment operations logged in n8n
- **Scalability**: Easy to add more payment methods or validation rules

The secure payment system is now ready for production use!
