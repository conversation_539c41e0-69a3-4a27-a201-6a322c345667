-- Supabase Functions for n8n Integration
-- Run these in your Supabase SQL editor

-- Function to update user balance atomically
CREATE OR REPLACE FUNCTION update_user_balance(
  p_user_id UUID,
  p_amount NUMERIC
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_current_balance NUMERIC := 0;
  v_new_balance NUMERIC;
  v_result JSON;
BEGIN
  -- Get current balance or create new record
  SELECT current_balance INTO v_current_balance
  FROM balances
  WHERE user_id = p_user_id;
  
  -- If no balance record exists, create one
  IF NOT FOUND THEN
    v_current_balance := 0;
  END IF;
  
  -- Calculate new balance
  v_new_balance := v_current_balance + p_amount;
  
  -- Update or insert balance record
  INSERT INTO balances (user_id, current_balance, updated_at)
  VALUES (p_user_id, v_new_balance, NOW())
  ON CONFLICT (user_id)
  DO UPDATE SET
    current_balance = v_new_balance,
    updated_at = NOW();
  
  -- Return result
  v_result := json_build_object(
    'success', true,
    'user_id', p_user_id,
    'previous_balance', v_current_balance,
    'amount_added', p_amount,
    'new_balance', v_new_balance,
    'updated_at', NOW()
  );
  
  RETURN v_result;
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM,
      'user_id', p_user_id
    );
END;
$$;

-- Function to validate JWT token (for n8n authentication)
CREATE OR REPLACE FUNCTION validate_jwt_token(
  p_token TEXT
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
  v_email TEXT;
  v_result JSON;
BEGIN
  -- Validate JWT token using Supabase auth
  SELECT 
    (auth.jwt() ->> 'sub')::UUID,
    auth.jwt() ->> 'email'
  INTO v_user_id, v_email;
  
  -- Check if user exists and is valid
  IF v_user_id IS NULL THEN
    RETURN json_build_object(
      'valid', false,
      'error', 'Invalid or expired token'
    );
  END IF;
  
  -- Return validation result
  v_result := json_build_object(
    'valid', true,
    'user_id', v_user_id,
    'email', v_email,
    'validated_at', NOW()
  );
  
  RETURN v_result;
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'valid', false,
      'error', SQLERRM
    );
END;
$$;

-- Function to get payment status
CREATE OR REPLACE FUNCTION get_payment_status(
  p_payment_id UUID DEFAULT NULL,
  p_invoice_id TEXT DEFAULT NULL,
  p_user_id UUID DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_payment RECORD;
  v_result JSON;
BEGIN
  -- Get payment record
  SELECT *
  INTO v_payment
  FROM payments
  WHERE 
    (p_payment_id IS NULL OR id = p_payment_id) AND
    (p_invoice_id IS NULL OR invoice_id = p_invoice_id) AND
    (p_user_id IS NULL OR user_id = p_user_id)
  ORDER BY created_at DESC
  LIMIT 1;
  
  -- Check if payment found
  IF NOT FOUND THEN
    RETURN json_build_object(
      'found', false,
      'error', 'Payment not found'
    );
  END IF;
  
  -- Return payment status
  v_result := json_build_object(
    'found', true,
    'payment_id', v_payment.id,
    'user_id', v_payment.user_id,
    'amount', v_payment.amount,
    'status', v_payment.status,
    'invoice_id', v_payment.invoice_id,
    'external_id', v_payment.external_id,
    'created_at', v_payment.created_at,
    'paid_at', v_payment.paid_at
  );
  
  RETURN v_result;
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'found', false,
      'error', SQLERRM
    );
END;
$$;

-- Function to process payment completion (idempotent)
CREATE OR REPLACE FUNCTION complete_payment(
  p_invoice_id TEXT,
  p_amount NUMERIC,
  p_paid_at TIMESTAMP DEFAULT NOW()
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_payment RECORD;
  v_balance_result JSON;
  v_result JSON;
BEGIN
  -- Get payment record
  SELECT *
  INTO v_payment
  FROM payments
  WHERE invoice_id = p_invoice_id;
  
  -- Check if payment exists
  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Payment not found',
      'invoice_id', p_invoice_id
    );
  END IF;
  
  -- Check if already completed (idempotent)
  IF v_payment.status = 'completed' THEN
    RETURN json_build_object(
      'success', true,
      'already_completed', true,
      'payment_id', v_payment.id,
      'user_id', v_payment.user_id
    );
  END IF;
  
  -- Update payment status
  UPDATE payments
  SET 
    status = 'completed',
    paid_at = p_paid_at,
    updated_at = NOW()
  WHERE invoice_id = p_invoice_id;
  
  -- Update user balance
  SELECT update_user_balance(v_payment.user_id, p_amount)
  INTO v_balance_result;
  
  -- Create transaction record
  INSERT INTO transactions (user_id, type, amount, description, date)
  VALUES (
    v_payment.user_id,
    'credit',
    p_amount,
    'Credit purchase: ' || p_amount || ' credits',
    CURRENT_DATE
  );
  
  -- Return success result
  v_result := json_build_object(
    'success', true,
    'payment_id', v_payment.id,
    'user_id', v_payment.user_id,
    'amount', p_amount,
    'balance_update', v_balance_result,
    'completed_at', p_paid_at
  );
  
  RETURN v_result;
  
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM,
      'invoice_id', p_invoice_id
    );
END;
$$;

-- Grant execute permissions to service role
GRANT EXECUTE ON FUNCTION update_user_balance(UUID, NUMERIC) TO service_role;
GRANT EXECUTE ON FUNCTION validate_jwt_token(TEXT) TO service_role;
GRANT EXECUTE ON FUNCTION get_payment_status(UUID, TEXT, UUID) TO service_role;
GRANT EXECUTE ON FUNCTION complete_payment(TEXT, NUMERIC, TIMESTAMP) TO service_role;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_payments_invoice_id ON payments(invoice_id);
CREATE INDEX IF NOT EXISTS idx_payments_user_status ON payments(user_id, status);
CREATE INDEX IF NOT EXISTS idx_transactions_user_date ON transactions(user_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_balances_user_id ON balances(user_id);
