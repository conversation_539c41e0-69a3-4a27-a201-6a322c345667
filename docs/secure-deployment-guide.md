# Secure Payment System Deployment Guide

This guide explains how to deploy the secure payment system with n8n as middleware.

## Architecture Overview

```
Frontend (React) → n8n Workflows → Xendit API → Supabase Database
```

### Security Benefits:
- ✅ No secret keys exposed in frontend
- ✅ Authentication validation at n8n layer
- ✅ Centralized payment processing logic
- ✅ Audit trail and monitoring capabilities
- ✅ Rate limiting and request validation

## Prerequisites

1. **n8n Instance** (self-hosted or cloud)
2. **Supabase Project** with RLS policies
3. **Xendit Account** with API keys
4. **Domain/Hosting** for production deployment

## Step 1: Environment Configuration

### Frontend Environment Variables (.env)
```bash
# Supabase Configuration
VITE_SUPABASE_URL="https://your-project.supabase.co"
VITE_SUPABASE_ANON_KEY="your_supabase_anon_key"

# n8n Configuration (Frontend)
VITE_N8N_WEBHOOK_URL="https://your-n8n-instance.com/webhook"
VITE_N8N_API_URL="https://your-n8n-instance.com/api/v1"

# Xendit Public Key (Safe for frontend)
VITE_XENDIT_PUBLIC_KEY="xnd_public_production_your_public_key"
```

### n8n Environment Variables
```bash
# Xendit Configuration (Server-side only)
XENDIT_SECRET_KEY="xnd_production_your_secret_key"
XENDIT_WEBHOOK_TOKEN="your_webhook_verification_token"

# Supabase Configuration (Server-side)
SUPABASE_URL="https://your-project.supabase.co"
SUPABASE_SERVICE_ROLE_KEY="your_service_role_key"

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=secure_password
WEBHOOK_URL=https://your-n8n-instance.com
```

## Step 2: Deploy n8n Instance

### Option A: Docker Deployment
```yaml
# docker-compose.yml
version: '3.8'
services:
  n8n:
    image: n8nio/n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_USER}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_PASSWORD}
      - WEBHOOK_URL=${WEBHOOK_URL}
      - XENDIT_SECRET_KEY=${XENDIT_SECRET_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    volumes:
      - n8n_data:/home/<USER>/.n8n
    restart: unless-stopped

volumes:
  n8n_data:
```

### Option B: Cloud Deployment (n8n Cloud)
1. Sign up for n8n Cloud
2. Configure environment variables in n8n Cloud dashboard
3. Import workflow templates

## Step 3: Setup Supabase Functions

Run the SQL functions in your Supabase SQL editor:

```sql
-- Copy content from docs/supabase-functions.sql
-- This includes:
-- - update_user_balance()
-- - validate_jwt_token()
-- - get_payment_status()
-- - complete_payment()
```

## Step 4: Import n8n Workflows

1. **Access n8n Interface**
   - Navigate to your n8n instance
   - Login with basic auth credentials

2. **Import Workflows**
   - Create new workflow for "Create Payment Invoice"
   - Create new workflow for "Process Payment Webhook"
   - Create new workflow for "Health Check"
   - Follow the detailed setup in `docs/n8n-setup.md`

3. **Configure Webhook URLs**
   - Set webhook paths: `/create-payment`, `/xendit-payment`, `/health`
   - Enable authentication for sensitive endpoints

## Step 5: Configure Xendit Webhooks

1. **Login to Xendit Dashboard**
2. **Navigate to Webhooks Settings**
3. **Add Webhook URL**:
   ```
   https://your-n8n-instance.com/webhook/xendit-payment
   ```
4. **Select Events**: `invoice.paid`
5. **Set Verification Token**: Use the same token in n8n environment

## Step 6: Deploy Frontend Application

### Netlify Deployment
```bash
# Build command
npm run build

# Environment variables in Netlify dashboard
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
VITE_N8N_WEBHOOK_URL=https://your-n8n-instance.com/webhook
VITE_N8N_API_URL=https://your-n8n-instance.com/api/v1
VITE_XENDIT_PUBLIC_KEY=xnd_public_production_your_key
```

### Vercel Deployment
```bash
# vercel.json
{
  "env": {
    "VITE_SUPABASE_URL": "https://your-project.supabase.co",
    "VITE_SUPABASE_ANON_KEY": "your_anon_key",
    "VITE_N8N_WEBHOOK_URL": "https://your-n8n-instance.com/webhook",
    "VITE_N8N_API_URL": "https://your-n8n-instance.com/api/v1",
    "VITE_XENDIT_PUBLIC_KEY": "xnd_public_production_your_key"
  }
}
```

## Step 7: Security Hardening

### n8n Security
1. **Enable HTTPS** for all n8n endpoints
2. **Configure Rate Limiting**
3. **Set up IP Whitelisting** (if needed)
4. **Enable Request Logging**
5. **Regular Security Updates**

### Supabase Security
1. **Review RLS Policies**
2. **Audit Database Permissions**
3. **Enable Database Logging**
4. **Regular Backup Schedule**

### Xendit Security
1. **Use Production API Keys**
2. **Enable Webhook Signature Verification**
3. **Monitor API Usage**
4. **Set up Fraud Detection**

## Step 8: Testing

### Development Testing
```bash
# Test n8n connection
npm run dev
# Open browser console and run:
testN8nWorkflows()
```

### Production Testing
1. **Create Test Payment**
   - Use small amount (e.g., Rp 10,000)
   - Verify invoice creation
   - Complete payment in Xendit dashboard

2. **Verify Webhook Processing**
   - Check n8n execution logs
   - Verify database updates
   - Confirm balance updates

3. **Test Authentication**
   - Try accessing endpoints without auth
   - Verify token validation
   - Test session expiry handling

## Step 9: Monitoring

### n8n Monitoring
- Set up execution monitoring
- Configure error notifications
- Monitor webhook response times

### Application Monitoring
- Track payment success rates
- Monitor API response times
- Set up error alerting

### Database Monitoring
- Monitor query performance
- Track payment completion rates
- Set up backup verification

## Step 10: Maintenance

### Regular Tasks
1. **Update Dependencies**
   - n8n version updates
   - Security patches
   - Library updates

2. **Monitor Logs**
   - Check for errors
   - Analyze performance
   - Review security events

3. **Backup Verification**
   - Test database restores
   - Verify n8n workflow backups
   - Document recovery procedures

## Troubleshooting

### Common Issues

1. **n8n Connection Failed**
   ```bash
   # Check n8n status
   curl https://your-n8n-instance.com/webhook/health
   
   # Verify environment variables
   echo $VITE_N8N_WEBHOOK_URL
   ```

2. **Authentication Errors**
   - Verify JWT token validity
   - Check Supabase session
   - Confirm user permissions

3. **Payment Processing Fails**
   - Check Xendit API status
   - Verify webhook configuration
   - Review n8n execution logs

4. **Database Connection Issues**
   - Verify Supabase credentials
   - Check RLS policies
   - Review service role permissions

### Support Contacts
- **n8n Support**: [n8n Community](https://community.n8n.io/)
- **Supabase Support**: [Supabase Discord](https://discord.supabase.com/)
- **Xendit Support**: [Xendit Help Center](https://help.xendit.co/)

## Security Checklist

- [ ] No secret keys in frontend code
- [ ] HTTPS enabled for all endpoints
- [ ] Authentication required for sensitive operations
- [ ] Rate limiting configured
- [ ] Webhook signature verification enabled
- [ ] Database RLS policies active
- [ ] Regular security audits scheduled
- [ ] Backup and recovery procedures tested
- [ ] Monitoring and alerting configured
- [ ] Documentation up to date
