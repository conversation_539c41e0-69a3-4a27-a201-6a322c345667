{"name": "Health Check", "nodes": [{"parameters": {"httpMethod": "GET", "path": "health", "responseMode": "responseNode", "options": {}}, "id": "h1e2a3l4-t5h6-c7h8-e9c0-k1a2b3c4d5e6", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "health"}, {"parameters": {"functionCode": "// Return health status\nreturn {\n  json: {\n    status: 'active',\n    timestamp: new Date().toISOString(),\n    service: 'n8n-payment-processor',\n    version: '1.0.0',\n    endpoints: {\n      'create-payment': 'https://n8n-vaw3sogm.runner.web.id/webhook-test/create-payment',\n      'xendit-payment': 'https://n8n-vaw3sogm.runner.web.id/webhook-test/xendit-payment',\n      'health': 'https://n8n-vaw3sogm.runner.web.id/webhook-test/health'\n    },\n    uptime: 0,\n    message: 'Payment processing service is running',\n    development: {\n      'redirect_urls': {\n        'success': 'http://localhost:5173/dashboard/billing?payment=success',\n        'failure': 'http://localhost:5173/dashboard/billing?payment=failed'\n      }\n    }\n  }\n};"}, "id": "e2a3l4t5-h6c7-h8e9-c0k1-a2b3c4d5e6f7", "name": "Health Status", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{$json}}", "options": {}}, "id": "a3l4t5h6-c7h8-e9c0-k1a2-b3c4d5e6f7a8", "name": "Send Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}], "connections": {"Webhook": {"main": [[{"node": "Health Status", "type": "main", "index": 0}]]}, "Health Status": {"main": [[{"node": "Send Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "id": "health-check", "meta": {"instanceId": "n8n-payment-processor"}, "tags": []}