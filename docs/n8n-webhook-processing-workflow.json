{"name": "Payment Webhook Processing", "nodes": [{"parameters": {"httpMethod": "POST", "path": "xendit-payment", "responseMode": "responseNode", "options": {}}, "id": "w1x2y3z4-a5b6-c7d8-e9f0-1a2b3c4d5e6f", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "xendit-payment"}, {"parameters": {"functionCode": "// Validate Xendit webhook and extract payment data\nconst headers = $input.first().headers;\nconst body = $input.first().body;\n\n// Validate webhook token\nconst webhookToken = headers['x-callback-token'];\nconst expectedToken = 'sGqcEXjShdnLd4S6DITIbOxbbKWxsPK0018WQb8JqtoHxlo9';\n\nif (webhookToken !== expectedToken) {\n  return {\n    json: {\n      success: false,\n      error: 'UNAUTHORIZED_WEBHOOK',\n      message: 'Invalid webhook token',\n      statusCode: 401\n    }\n  };\n}\n\n// Validate webhook data\nif (!body || !body.id || !body.status || !body.external_id) {\n  return {\n    json: {\n      success: false,\n      error: 'INVALID_WEBHOOK_DATA',\n      message: 'Missing required webhook fields',\n      statusCode: 400\n    }\n  };\n}\n\n// Only process PAID status\nif (body.status !== 'PAID') {\n  return {\n    json: {\n      success: true,\n      skip: true,\n      reason: `Ignoring status: ${body.status}`,\n      status: body.status,\n      message: 'Webhook received but not processed (status not PAID)'\n    }\n  };\n}\n\n// Extract user ID from external_id (format: topup_userId_timestamp)\nconst userIdMatch = body.external_id.match(/^topup_([^_]+)_/);\nif (!userIdMatch) {\n  return {\n    json: {\n      success: false,\n      error: 'INVALID_EXTERNAL_ID',\n      message: 'Invalid external_id format',\n      statusCode: 400\n    }\n  };\n}\n\nconst userId = userIdMatch[1];\n\n// Validate amount\nconst amount = parseFloat(body.amount);\nif (isNaN(amount) || amount <= 0) {\n  return {\n    json: {\n      success: false,\n      error: 'INVALID_AMOUNT',\n      message: 'Invalid payment amount',\n      statusCode: 400\n    }\n  };\n}\n\n// Return validated webhook data\nreturn {\n  json: {\n    success: true,\n    skip: false,\n    userId: userId,\n    invoiceId: body.id,\n    amount: amount,\n    externalId: body.external_id,\n    paidAt: body.paid_at || new Date().toISOString(),\n    status: body.status,\n    webhookData: body\n  }\n};"}, "id": "x2y3z4a5-b6c7-d8e9-f0a1-2b3c4d5e6f7a", "name": "Validate Webhook", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.success}}", "value2": true}, {"value1": "={{$json.skip}}", "value2": false}]}}, "id": "y3z4a5b6-c7d8-e9f0-a1b2-3c4d5e6f7a8b", "name": "Check Should Process", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"method": "PATCH", "url": "https://iozuzywvrncfejmlnsfu.supabase.co/rest/v1/payments?external_id=eq.{{$node['Validate Webhook'].json.externalId}}", "authentication": "none", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlvenV6eXd2cm5jZmVqbWxuc2Z1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkyMzI2NiwiZXhwIjoyMDY2NDk5MjY2fQ.VdNN7_RpZdu_HN2VHGrR3pFAqz32mKRtazgdet5am6s"}, {"name": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlvenV6eXd2cm5jZmVqbWxuc2Z1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkyMzI2NiwiZXhwIjoyMDY2NDk5MjY2fQ.VdNN7_RpZdu_HN2VHGrR3pFAqz32mKRtazgdet5am6s"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Prefer", "value": "return=representation"}]}, "sendBody": true, "jsonParameters": true, "bodyParametersJson": "{\n  \"status\": \"COMPLETED\",\n  \"paid_at\": \"{{$node['Validate Webhook'].json.paidAt}}\",\n  \"updated_at\": \"{{new Date().toISOString()}}\"\n}", "options": {}}, "id": "z4a5b6c7-d8e9-f0a1-b2c3-4d5e6f7a8b9c", "name": "Complete Payment", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200]}, {"parameters": {"method": "POST", "url": "https://iozuzywvrncfejmlnsfu.supabase.co/rest/v1/rpc/update_user_balance", "authentication": "none", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlvenV6eXd2cm5jZmVqbWxuc2Z1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkyMzI2NiwiZXhwIjoyMDY2NDk5MjY2fQ.VdNN7_RpZdu_HN2VHGrR3pFAqz32mKRtazgdet5am6s"}, {"name": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlvenV6eXd2cm5jZmVqbWxuc2Z1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkyMzI2NiwiZXhwIjoyMDY2NDk5MjY2fQ.VdNN7_RpZdu_HN2VHGrR3pFAqz32mKRtazgdet5am6s"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "jsonParameters": true, "bodyParametersJson": "{\n  \"p_user_id\": \"{{$node['Validate Webhook'].json.userId}}\",\n  \"p_amount\": {{$node['Validate Webhook'].json.amount}}\n}", "options": {}}, "id": "d8e9f0a1-b2c3-d4e5-f6a7-8b9c0d1e2f3a", "name": "Update User Balance", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 200]}, {"parameters": {"method": "POST", "url": "https://iozuzywvrncfejmlnsfu.supabase.co/rest/v1/transactions", "authentication": "none", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlvenV6eXd2cm5jZmVqbWxuc2Z1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkyMzI2NiwiZXhwIjoyMDY2NDk5MjY2fQ.VdNN7_RpZdu_HN2VHGrR3pFAqz32mKRtazgdet5am6s"}, {"name": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlvenV6eXd2cm5jZmVqbWxuc2Z1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkyMzI2NiwiZXhwIjoyMDY2NDk5MjY2fQ.VdNN7_RpZdu_HN2VHGrR3pFAqz32mKRtazgdet5am6s"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "jsonParameters": true, "bodyParametersJson": "{\n  \"user_id\": \"{{$node['Validate Webhook'].json.userId}}\",\n  \"type\": \"purchase\",\n  \"amount\": {{$node['Validate Webhook'].json.amount}},\n  \"description\": \"Credit purchase: {{$node['Validate Webhook'].json.amount}} credits\",\n  \"date\": \"{{new Date().toISOString().split('T')[0]}}\",\n  \"payment_id\": \"{{$node['Validate Webhook'].json.externalId}}\"\n}", "options": {}}, "id": "e9f0a1b2-c3d4-e5f6-a7b8-9c0d1e2f3a4b", "name": "Create Transaction Record", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1340, 200]}, {"parameters": {"functionCode": "// Format successful payment completion response\nconst webhookData = $node['Validate Webhook'].json;\nconst paymentResult = $node['Complete Payment'].json;\nconst balanceResult = $node['Update User Balance'].json;\nconst transactionResult = $node['Create Transaction Record'].json;\n\nreturn {\n  json: {\n    success: true,\n    message: 'Payment processed successfully',\n    invoiceId: webhookData.invoiceId,\n    userId: webhookData.userId,\n    amount: webhookData.amount,\n    externalId: webhookData.externalId,\n    processedAt: new Date().toISOString(),\n    results: {\n      payment: paymentResult,\n      balance: balanceResult,\n      transaction: transactionResult\n    }\n  }\n};"}, "id": "a5b6c7d8-e9f0-a1b2-c3d4-5e6f7a8b9c0d", "name": "Format Success Response", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1560, 200]}, {"parameters": {"functionCode": "// Format response for skipped or error cases\nconst webhookData = $node['Validate Webhook'].json;\n\nif (webhookData.skip) {\n  return {\n    json: {\n      success: true,\n      message: webhookData.reason || 'Webhook received but not processed',\n      status: webhookData.status,\n      skipped: true,\n      timestamp: new Date().toISOString()\n    }\n  };\n}\n\n// Error case\nreturn {\n  json: {\n    success: false,\n    error: webhookData.error || 'WEBHOOK_PROCESSING_ERROR',\n    message: webhookData.message || 'Failed to process webhook',\n    statusCode: webhookData.statusCode || 400,\n    timestamp: new Date().toISOString()\n  }\n};"}, "id": "b6c7d8e9-f0a1-b2c3-d4e5-6f7a8b9c0d1e", "name": "Format Skip/Error Response", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{$json}}", "options": {}}, "id": "c7d8e9f0-a1b2-c3d4-e5f6-7a8b9c0d1e2f", "name": "Send Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1780, 300]}], "connections": {"Webhook": {"main": [[{"node": "Validate Webhook", "type": "main", "index": 0}]]}, "Validate Webhook": {"main": [[{"node": "Check Should Process", "type": "main", "index": 0}]]}, "Check Should Process": {"main": [[{"node": "Complete Payment", "type": "main", "index": 0}], [{"node": "Format Skip/Error Response", "type": "main", "index": 0}]]}, "Complete Payment": {"main": [[{"node": "Update User Balance", "type": "main", "index": 0}]]}, "Update User Balance": {"main": [[{"node": "Create Transaction Record", "type": "main", "index": 0}]]}, "Create Transaction Record": {"main": [[{"node": "Format Success Response", "type": "main", "index": 0}]]}, "Format Success Response": {"main": [[{"node": "Send Response", "type": "main", "index": 0}]]}, "Format Skip/Error Response": {"main": [[{"node": "Send Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "id": "payment-webhook-processing", "meta": {"instanceId": "n8n-payment-processor"}, "tags": []}