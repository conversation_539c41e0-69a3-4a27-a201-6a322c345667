# n8n Quick Setup Checklist

## ✅ Pre-Setup (Completed)
- [x] n8n installed and running at `https://n8n-vaw3sogm.runner.web.id`
- [x] Environment variables updated in `.env`
- [x] Frontend code updated to use secure payment service

## 🔧 n8n Configuration (Do This Now)

### 1. Create Credentials (Environment Variables are Enterprise-only)
Go to `https://n8n-vaw3sogm.runner.web.id/credentials`:

**Xendit API Credential:**
- Type: `HTTP Basic Auth`
- Name: `Xendit API`
- User: `xnd_development_XzOz51cv5CryV5bQWytvZQZFhjFFKZnqc9MygvOnBait0QvQ1UvOlSREu9TKq`
- Password: (leave empty)

**Supabase Service Credential:**
- Type: `HTTP Header Auth`
- Name: `Supabase Service`
- Header Name: `Authorization`
- Header Value: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your_actual_service_role_key_here`

**Supabase API Key Credential:**
- Type: `HTTP Header Auth`
- Name: `Supabase API Key`
- Header Name: `apikey`
- Header Value: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your_actual_anon_key_here`

### 2. Create Workflows

#### Workflow 1: Payment Invoice Creation
1. **Create New Workflow** → Name: `Payment Invoice Creation`
2. **Add 6 nodes in this order:**
   - Webhook Trigger (path: `create-payment`)
   - Function: `Validate Authentication`
   - HTTP Request: `Create Xendit Invoice`
   - HTTP Request: `Store Payment Record`
   - Function: `Format Response`
3. **Copy configurations** from `docs/n8n-implementation-guide.md`
4. **Save and Activate**

#### Workflow 2: Payment Webhook Processing
1. **Create New Workflow** → Name: `Payment Webhook Processing`
2. **Add 4 nodes in this order:**
   - Webhook Trigger (path: `xendit-payment`)
   - Function: `Validate Webhook`
   - HTTP Request: `Complete Payment`
   - Function: `Webhook Response`
3. **Copy configurations** from `docs/n8n-implementation-guide.md`
4. **Save and Activate**

#### Workflow 3: Health Check
1. **Create New Workflow** → Name: `Health Check`
2. **Add 2 nodes:**
   - Webhook Trigger (path: `health`, method: GET)
   - Function: Return status
3. **Save and Activate**

## 🧪 Testing (After Setup)

### 1. Test Health Check
```bash
curl https://n8n-vaw3sogm.runner.web.id/webhook/health
```
**Expected Response:**
```json
{
  "status": "active",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "n8n-payment-processor"
}
```

### 2. Test Frontend Integration
1. **Start your React app**: `npm run dev`
2. **Open browser console**
3. **Run**: `testN8nWorkflows()`
4. **Should see**: `✅ n8n connection successful`

### 3. Test Payment Creation (Manual)
1. **Login to your app**
2. **Try to create a top-up**
3. **Check n8n execution logs** for any errors
4. **Verify payment record** created in Supabase

## 🔒 Security Verification

### Check These Items:
- [ ] No `VITE_XENDIT_SECRET_KEY` in `.env` (should be removed)
- [ ] n8n environment variables configured
- [ ] Webhook authentication enabled
- [ ] All workflows activated
- [ ] Frontend using `securePaymentService`

### Verify Security:
1. **Check browser DevTools** → Sources → No secret keys visible
2. **Network tab** → Requests go to n8n, not directly to Xendit
3. **Console** → No Xendit SDK errors

## 🚨 Common Issues & Solutions

### Issue: "n8n connection failed"
**Solution:** Check if n8n is running and accessible at the URL

### Issue: "Environment variable not found"
**Solution:** Verify environment variables are set in n8n settings

### Issue: "Unauthorized webhook"
**Solution:** Check webhook token matches in both n8n and Xendit

### Issue: "Supabase function not found"
**Solution:** Run the SQL functions from `docs/supabase-functions.sql`

## 📋 Final Steps

### 1. Configure Xendit Webhook
- **Login to Xendit Dashboard**
- **Go to Webhooks**
- **Add URL**: `https://n8n-vaw3sogm.runner.web.id/webhook/xendit-payment`
- **Select Events**: `invoice.paid`
- **Verification Token**: Use same as `XENDIT_WEBHOOK_TOKEN`

### 2. Update Production URLs
When deploying to production, update:
- n8n webhook URLs in `.env`
- Xendit webhook URL in dashboard
- Success/failure redirect URLs in n8n workflow

### 3. Monitor and Test
- **Check n8n execution logs** regularly
- **Monitor payment success rates**
- **Test with small amounts** first

## 🎯 Success Criteria

You'll know it's working when:
- ✅ Health check returns 200 OK
- ✅ Frontend top-up creates payment without errors
- ✅ Payment completion updates balance
- ✅ No secret keys visible in browser
- ✅ All requests go through n8n middleware

## 📞 Need Help?

If you encounter issues:
1. **Check n8n execution logs** for detailed error messages
2. **Verify environment variables** are correctly set
3. **Test each workflow individually** using the test button
4. **Check network connectivity** between services

**Reference Documents:**
- `docs/n8n-implementation-guide.md` - Detailed configurations
- `docs/supabase-functions.sql` - Database functions
- `docs/secure-deployment-guide.md` - Production deployment
