# n8n Setup Guide for Secure Payment Processing

This guide explains how to set up n8n workflows to handle payment processing securely.

## Prerequisites

1. **n8n Installation**
   ```bash
   npm install -g n8n
   # or
   docker run -it --rm --name n8n -p 5678:5678 n8nio/n8n
   ```

2. **Environment Variables**
   Set these in your n8n environment:
   ```bash
   XENDIT_SECRET_KEY=xnd_development_your_secret_key_here
   XENDIT_WEBHOOK_TOKEN=your_webhook_verification_token_here
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
   ```

## Workflow 1: Create Payment Invoice

### Webhook Trigger
- **URL**: `/webhook/create-payment`
- **Method**: POST
- **Authentication**: Required (Bearer token)

### Workflow Steps:

1. **Webhook Node** (Trigger)
   - Path: `create-payment`
   - Authentication: Header Auth
   - Header Name: `Authorization`

2. **Function Node** (Validate Auth)
   ```javascript
   // Validate user authentication
   const authHeader = $node["Webhook"].json["headers"]["authorization"];
   const userId = $node["Webhook"].json["headers"]["x-user-id"];
   
   if (!authHeader || !authHeader.startsWith('Bearer ')) {
     throw new Error('Unauthorized: Missing or invalid token');
   }
   
   const token = authHeader.replace('Bearer ', '');
   
   // Validate token with Supabase (implement JWT validation)
   return {
     token,
     userId,
     validated: true
   };
   ```

3. **HTTP Request Node** (Create Xendit Invoice)
   - **URL**: `https://api.xendit.co/v2/invoices`
   - **Method**: POST
   - **Authentication**: Basic Auth
     - Username: `{{ $env.XENDIT_SECRET_KEY }}`
     - Password: (leave empty)
   - **Headers**:
     ```json
     {
       "Content-Type": "application/json"
     }
     ```
   - **Body**:
     ```json
     {
       "external_id": "topup_{{ $node['Function'].json['userId'] }}_{{ new Date().getTime() }}",
       "amount": "{{ $node['Webhook'].json['body']['amount'] }}",
       "description": "{{ $node['Webhook'].json['body']['description'] }}",
       "invoice_duration": 86400,
       "customer": {
         "email": "{{ $node['Webhook'].json['body']['userEmail'] }}"
       },
       "success_redirect_url": "https://cloone-sumopod.netlify.app/dashboard/billing?payment=success",
       "failure_redirect_url": "https://cloone-sumopod.netlify.app/dashboard/billing?payment=failed",
       "currency": "IDR",
       "items": [
         {
           "name": "Balance Top-up",
           "quantity": 1,
           "price": "{{ $node['Webhook'].json['body']['amount'] }}"
         }
       ]
     }
     ```

4. **HTTP Request Node** (Store Payment Record)
   - **URL**: `{{ $env.SUPABASE_URL }}/rest/v1/payments`
   - **Method**: POST
   - **Headers**:
     ```json
     {
       "apikey": "{{ $env.SUPABASE_SERVICE_ROLE_KEY }}",
       "Authorization": "Bearer {{ $env.SUPABASE_SERVICE_ROLE_KEY }}",
       "Content-Type": "application/json",
       "Prefer": "return=representation"
     }
     ```
   - **Body**:
     ```json
     {
       "user_id": "{{ $node['Function'].json['userId'] }}",
       "amount": "{{ $node['Webhook'].json['body']['amount'] }}",
       "credits": "{{ $node['Webhook'].json['body']['amount'] }}",
       "status": "pending",
       "external_id": "{{ $node['HTTP Request'].json['external_id'] }}",
       "invoice_id": "{{ $node['HTTP Request'].json['id'] }}",
       "invoice_url": "{{ $node['HTTP Request'].json['invoice_url'] }}",
       "date": "{{ new Date().toISOString().split('T')[0] }}"
     }
     ```

5. **Function Node** (Format Response)
   ```javascript
   const xenditResponse = $node["HTTP Request"].json;
   const supabaseResponse = $node["HTTP Request1"].json;
   
   return {
     success: true,
     paymentUrl: xenditResponse.invoice_url,
     paymentId: supabaseResponse[0].id,
     invoiceId: xenditResponse.id,
     externalId: xenditResponse.external_id
   };
   ```

## Workflow 2: Process Payment Webhook

### Webhook Trigger
- **URL**: `/webhook/xendit-payment`
- **Method**: POST
- **Authentication**: Token validation

### Workflow Steps:

1. **Webhook Node** (Trigger)
   - Path: `xendit-payment`
   - Authentication: Header Auth
   - Header Name: `x-callback-token`

2. **Function Node** (Validate Webhook)
   ```javascript
   // Validate webhook token
   const webhookToken = $node["Webhook"].json["headers"]["x-callback-token"];
   const expectedToken = $env.XENDIT_WEBHOOK_TOKEN;
   
   if (webhookToken !== expectedToken) {
     throw new Error('Unauthorized webhook');
   }
   
   const webhookData = $node["Webhook"].json["body"];
   
   // Only process PAID status
   if (webhookData.status !== 'PAID') {
     return { skip: true, reason: 'Not a paid status' };
   }
   
   // Extract user ID from external_id
   const userIdMatch = webhookData.external_id.match(/^topup_([^_]+)_/);
   if (!userIdMatch) {
     throw new Error('Invalid external_id format');
   }
   
   return {
     userId: userIdMatch[1],
     invoiceId: webhookData.id,
     amount: webhookData.amount,
     externalId: webhookData.external_id,
     paidAt: webhookData.paid_at || new Date().toISOString()
   };
   ```

3. **HTTP Request Node** (Update Payment Status)
   - **URL**: `{{ $env.SUPABASE_URL }}/rest/v1/payments?invoice_id=eq.{{ $node['Function'].json['invoiceId'] }}`
   - **Method**: PATCH
   - **Headers**:
     ```json
     {
       "apikey": "{{ $env.SUPABASE_SERVICE_ROLE_KEY }}",
       "Authorization": "Bearer {{ $env.SUPABASE_SERVICE_ROLE_KEY }}",
       "Content-Type": "application/json"
     }
     ```
   - **Body**:
     ```json
     {
       "status": "completed",
       "paid_at": "{{ $node['Function'].json['paidAt'] }}"
     }
     ```

4. **HTTP Request Node** (Update User Balance)
   - **URL**: `{{ $env.SUPABASE_URL }}/rest/v1/rpc/update_user_balance`
   - **Method**: POST
   - **Headers**:
     ```json
     {
       "apikey": "{{ $env.SUPABASE_SERVICE_ROLE_KEY }}",
       "Authorization": "Bearer {{ $env.SUPABASE_SERVICE_ROLE_KEY }}",
       "Content-Type": "application/json"
     }
     ```
   - **Body**:
     ```json
     {
       "p_user_id": "{{ $node['Function'].json['userId'] }}",
       "p_amount": "{{ $node['Function'].json['amount'] }}"
     }
     ```

5. **HTTP Request Node** (Create Transaction Record)
   - **URL**: `{{ $env.SUPABASE_URL }}/rest/v1/transactions`
   - **Method**: POST
   - **Headers**:
     ```json
     {
       "apikey": "{{ $env.SUPABASE_SERVICE_ROLE_KEY }}",
       "Authorization": "Bearer {{ $env.SUPABASE_SERVICE_ROLE_KEY }}",
       "Content-Type": "application/json"
     }
     ```
   - **Body**:
     ```json
     {
       "user_id": "{{ $node['Function'].json['userId'] }}",
       "type": "credit",
       "amount": "{{ $node['Function'].json['amount'] }}",
       "description": "Credit purchase: {{ $node['Function'].json['amount'] }} credits",
       "date": "{{ new Date().toISOString().split('T')[0] }}"
     }
     ```

## Workflow 3: Health Check

### Webhook Trigger
- **URL**: `/webhook/health`
- **Method**: GET

### Workflow Steps:

1. **Webhook Node** (Trigger)
   - Path: `health`

2. **Function Node** (Health Response)
   ```javascript
   return {
     status: 'active',
     timestamp: new Date().toISOString(),
     service: 'n8n-payment-processor'
   };
   ```

## Security Configuration

1. **Enable Authentication**
   - Set up basic authentication for n8n interface
   - Use environment variables for all sensitive data

2. **Network Security**
   - Run n8n behind a reverse proxy (nginx)
   - Use HTTPS for all webhook endpoints
   - Implement rate limiting

3. **Webhook Security**
   - Validate all incoming webhook tokens
   - Implement request signing verification
   - Log all webhook attempts for monitoring

## Testing

1. **Test Create Payment Workflow**
   ```bash
   curl -X POST http://localhost:5678/webhook/create-payment \
     -H "Authorization: Bearer your_jwt_token" \
     -H "X-User-ID: user_uuid" \
     -H "Content-Type: application/json" \
     -d '{
       "amount": 50000,
       "userId": "user_uuid",
       "userEmail": "<EMAIL>",
       "description": "Test top-up"
     }'
   ```

2. **Test Health Check**
   ```bash
   curl http://localhost:5678/webhook/health
   ```

## Deployment

1. **Production Environment Variables**
   ```bash
   N8N_BASIC_AUTH_ACTIVE=true
   N8N_BASIC_AUTH_USER=admin
   N8N_BASIC_AUTH_PASSWORD=secure_password
   WEBHOOK_URL=https://your-domain.com
   ```

2. **Docker Deployment**
   ```yaml
   version: '3.8'
   services:
     n8n:
       image: n8nio/n8n
       ports:
         - "5678:5678"
       environment:
         - XENDIT_SECRET_KEY=${XENDIT_SECRET_KEY}
         - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
       volumes:
         - n8n_data:/home/<USER>/.n8n
   ```
