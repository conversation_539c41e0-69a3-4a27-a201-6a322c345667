# n8n Implementation Guide - Secure Payment Processing

This guide provides step-by-step instructions for implementing secure payment workflows in your n8n instance at `https://n8n-vaw3sogm.runner.web.id`.

## Quick Import Method (Recommended)

### Option 1: Import Pre-configured Workflows

Instead of creating workflows manually, you can import the pre-configured JSON files:

1. **Download Workflow Files**:
   - `docs/n8n-payment-invoice-workflow.json` - Payment invoice creation
   - `docs/n8n-webhook-processing-workflow.json` - Webhook processing
   - `docs/n8n-health-check-workflow.json` - Health check

2. **Import to n8n**:
   - Go to `https://n8n-vaw3sogm.runner.web.id/workflows`
   - Click **"Import from File"** or **"+"** → **"Import from File"**
   - Upload each JSON file
   - Activate each workflow after import

3. **Update Credentials After Import**:
   - **Edit Payment Invoice Creation Workflow**:
     - Open "Create Xendit Invoice" node → Set credential to "Xendit API"
     - Open "Store Payment Record" node → Set credential to "Supabase Service"
   - **Edit Payment Webhook Processing Workflow**:
     - Open "Complete Payment" node → Set credential to "Supabase Service"
   - **Update Supabase URLs**:
     - Replace `https://wnqjqjqjqjqjqjqjqjqj.supabase.co` with your actual Supabase URL
     - Replace `your_anon_key_here` with your actual Supabase anon key
   - **Save and Activate** all workflows

## Authentication & Error Handling Features

The imported workflows include comprehensive authentication and error handling:

### **Payment Invoice Creation Workflow**:
- ✅ **JWT Token Validation**: Checks Authorization header and token validity
- ✅ **Token Expiry Check**: Validates token hasn't expired
- ✅ **User ID Matching**: Ensures token user matches request user
- ✅ **Input Validation**: Validates amount, email, and required fields
- ✅ **Login Redirect**: Returns `redirectToLogin: true` for unauthenticated users
- ✅ **Error Responses**: Structured error responses with specific error codes

### **Error Response Format**:
```json
{
  "success": false,
  "error": "AUTHENTICATION_REQUIRED",
  "message": "Please log in to continue",
  "redirectToLogin": true,
  "statusCode": 401
}
```

### **Success Response Format**:
```json
{
  "success": true,
  "paymentUrl": "https://checkout.xendit.co/web/...",
  "paymentId": "uuid-here",
  "invoiceId": "xendit-invoice-id",
  "amount": 50000,
  "message": "Payment invoice created successfully"
}
```

### **Webhook Processing Workflow**:
- ✅ **Webhook Token Validation**: Validates Xendit webhook token
- ✅ **Status Filtering**: Only processes 'PAID' status
- ✅ **Idempotent Processing**: Prevents duplicate processing
- ✅ **User ID Extraction**: Extracts user ID from external_id
- ✅ **Database Updates**: Updates payment status and user balance

### Option 2: Manual Creation (Alternative)

Follow the detailed steps below if you prefer to create workflows manually.

## Prerequisites Setup

### 1. Create Credentials in n8n

**Note**: Environment variables are only available in Enterprise plan. We'll use credentials instead.

1. **Go to Credentials** (`https://n8n-vaw3sogm.runner.web.id/credentials`)

2. **Create HTTP Basic Auth** for Xendit:
   - **Name**: `Xendit API`
   - **User**: `xnd_development_XzOz51cv5CryV5bQWytvZQZFhjFFKZnqc9MygvOnBait0QvQ1UvOlSREu9TKq`
   - **Password**: (leave empty)

3. **Create HTTP Header Auth** for Supabase:
   - **Name**: `Supabase Service`
   - **Header Name**: `Authorization`
   - **Header Value**: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your_actual_service_role_key_here`

4. **Create HTTP Header Auth** for Supabase API Key:
   - **Name**: `Supabase API Key`
   - **Header Name**: `apikey`
   - **Header Value**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your_actual_anon_key_here`

## Workflow 1: Payment Invoice Creation

### Step 1: Create New Workflow
1. Go to `Workflows` → `Create Workflow`
2. Name: `Payment Invoice Creation`

### Step 2: Add Webhook Trigger
1. **Add Node** → **Trigger** → **Webhook**
2. **Configuration**:
   - HTTP Method: `POST`
   - Path: `create-payment`
   - Authentication: `Header Auth`
   - Header Name: `Authorization`
   - Response Mode: `Respond to Webhook`

### Step 3: Add Authentication Validation Function
1. **Add Node** → **Code** → **Function**
2. **Name**: `Validate Authentication`
3. **JavaScript Code**:
```javascript
// Validate user authentication
const authHeader = $input.first().headers.authorization;
const userId = $input.first().headers['x-user-id'];
const body = $input.first().body;

if (!authHeader || !authHeader.startsWith('Bearer ')) {
  throw new Error('Unauthorized: Missing or invalid token');
}

if (!userId) {
  throw new Error('Unauthorized: Missing user ID');
}

// Extract token
const token = authHeader.replace('Bearer ', '');

// Validate required fields
if (!body.amount || !body.userId || !body.userEmail) {
  throw new Error('Missing required fields: amount, userId, userEmail');
}

// Validate user ID matches
if (body.userId !== userId) {
  throw new Error('Unauthorized: User ID mismatch');
}

return {
  token,
  userId,
  amount: body.amount,
  userEmail: body.userEmail,
  description: body.description || `Top-up balance - Rp ${body.amount.toLocaleString('id-ID')}`,
  externalId: `topup_${userId}_${Date.now()}`,
  timestamp: new Date().toISOString()
};
```

### Step 4: Add Xendit Invoice Creation
1. **Add Node** → **HTTP Request**
2. **Name**: `Create Xendit Invoice`
3. **Configuration**:
   - Method: `POST`
   - URL: `https://api.xendit.co/v2/invoices`
   - Authentication: `Existing Credential` → Select `Xendit API`
   - Headers:
     ```json
     {
       "Content-Type": "application/json"
     }
     ```
   - Body (JSON):
     ```json
     {
       "external_id": "{{ $('Validate Authentication').item.json.externalId }}",
       "amount": {{ $('Validate Authentication').item.json.amount }},
       "description": "{{ $('Validate Authentication').item.json.description }}",
       "invoice_duration": 86400,
       "customer": {
         "email": "{{ $('Validate Authentication').item.json.userEmail }}"
       },
       "success_redirect_url": "https://cloone-sumopod.netlify.app/dashboard/billing?payment=success",
       "failure_redirect_url": "https://cloone-sumopod.netlify.app/dashboard/billing?payment=failed",
       "currency": "IDR",
       "items": [
         {
           "name": "Balance Top-up",
           "quantity": 1,
           "price": {{ $('Validate Authentication').item.json.amount }}
         }
       ]
     }
     ```

### Step 5: Add Supabase Payment Record
1. **Add Node** → **HTTP Request**
2. **Name**: `Store Payment Record`
3. **Configuration**:
   - Method: `POST`
   - URL: `https://wnqjqjqjqjqjqjqjqjqj.supabase.co/rest/v1/payments`
   - Authentication: `Existing Credential` → Select `Supabase Service`
   - Additional Headers:
     ```json
     {
       "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your_anon_key_here",
       "Content-Type": "application/json",
       "Prefer": "return=representation"
     }
     ```
   - Body (JSON):
     ```json
     {
       "user_id": "{{ $('Validate Authentication').item.json.userId }}",
       "amount": {{ $('Validate Authentication').item.json.amount }},
       "credits": {{ $('Validate Authentication').item.json.amount }},
       "status": "pending",
       "external_id": "{{ $('Validate Authentication').item.json.externalId }}",
       "invoice_id": "{{ $('Create Xendit Invoice').item.json.id }}",
       "invoice_url": "{{ $('Create Xendit Invoice').item.json.invoice_url }}",
       "date": "{{ new Date().toISOString().split('T')[0] }}"
     }
     ```

### Step 6: Add Response Formatter
1. **Add Node** → **Code** → **Function**
2. **Name**: `Format Response`
3. **JavaScript Code**:
```javascript
const xenditResponse = $('Create Xendit Invoice').item.json;
const supabaseResponse = $('Store Payment Record').item.json;

return {
  success: true,
  paymentUrl: xenditResponse.invoice_url,
  paymentId: supabaseResponse[0].id,
  invoiceId: xenditResponse.id,
  externalId: xenditResponse.external_id,
  amount: xenditResponse.amount,
  status: xenditResponse.status
};
```

### Step 7: Connect Nodes
Connect the nodes in this order:
1. Webhook → Validate Authentication
2. Validate Authentication → Create Xendit Invoice
3. Create Xendit Invoice → Store Payment Record
4. Store Payment Record → Format Response

### Step 8: Save and Activate
1. **Save** the workflow
2. **Activate** the workflow
3. Note the webhook URL: `https://n8n-vaw3sogm.runner.web.id/webhook/create-payment`

## Workflow 2: Payment Webhook Processing

### Step 1: Create New Workflow
1. Go to `Workflows` → `Create Workflow`
2. Name: `Payment Webhook Processing`

### Step 2: Add Webhook Trigger
1. **Add Node** → **Trigger** → **Webhook**
2. **Configuration**:
   - HTTP Method: `POST`
   - Path: `xendit-payment`
   - Authentication: `Header Auth`
   - Header Name: `x-callback-token`
   - Response Mode: `Respond to Webhook`

### Step 3: Add Webhook Validation Function
1. **Add Node** → **Code** → **Function**
2. **Name**: `Validate Webhook`
3. **JavaScript Code**:
```javascript
// Validate webhook token (hardcoded since credentials not available in functions)
const webhookToken = $input.first().headers['x-callback-token'];
const expectedToken = 'sGqcEXjShdnLd4S6DITIbOxbbKWxsPK0018WQb8JqtoHxlo9';

if (webhookToken !== expectedToken) {
  throw new Error('Unauthorized webhook');
}

const webhookData = $input.first().body;

// Only process PAID status
if (webhookData.status !== 'PAID') {
  return { 
    skip: true, 
    reason: `Ignoring status: ${webhookData.status}`,
    status: webhookData.status
  };
}

// Extract user ID from external_id
const userIdMatch = webhookData.external_id.match(/^topup_([^_]+)_/);
if (!userIdMatch) {
  throw new Error('Invalid external_id format');
}

return {
  userId: userIdMatch[1],
  invoiceId: webhookData.id,
  amount: webhookData.amount,
  externalId: webhookData.external_id,
  paidAt: webhookData.paid_at || new Date().toISOString(),
  status: webhookData.status
};
```

### Step 4: Add Payment Completion Function
1. **Add Node** → **HTTP Request**
2. **Name**: `Complete Payment`
3. **Configuration**:
   - Method: `POST`
   - URL: `https://wnqjqjqjqjqjqjqjqjqj.supabase.co/rest/v1/rpc/complete_payment`
   - Authentication: `Existing Credential` → Select `Supabase Service`
   - Additional Headers:
     ```json
     {
       "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your_anon_key_here",
       "Content-Type": "application/json"
     }
     ```
   - Body (JSON):
     ```json
     {
       "p_invoice_id": "{{ $('Validate Webhook').item.json.invoiceId }}",
       "p_amount": {{ $('Validate Webhook').item.json.amount }},
       "p_paid_at": "{{ $('Validate Webhook').item.json.paidAt }}"
     }
     ```

### Step 5: Add Response Function
1. **Add Node** → **Code** → **Function**
2. **Name**: `Webhook Response`
3. **JavaScript Code**:
```javascript
const validationResult = $('Validate Webhook').item.json;

if (validationResult.skip) {
  return {
    success: true,
    message: validationResult.reason,
    status: validationResult.status
  };
}

const completionResult = $('Complete Payment').item.json;

return {
  success: completionResult.success,
  message: completionResult.success ? 'Payment processed successfully' : 'Payment processing failed',
  invoiceId: validationResult.invoiceId,
  userId: validationResult.userId,
  amount: validationResult.amount,
  processedAt: new Date().toISOString()
};
```

### Step 6: Connect Nodes
1. Webhook → Validate Webhook
2. Validate Webhook → Complete Payment (only if not skipped)
3. Complete Payment → Webhook Response

### Step 7: Save and Activate
1. **Save** the workflow
2. **Activate** the workflow
3. Note the webhook URL: `https://n8n-vaw3sogm.runner.web.id/webhook/xendit-payment`

## Workflow 3: Health Check

### Step 1: Create Simple Health Check
1. **Create New Workflow**: `Health Check`
2. **Add Webhook Trigger**:
   - Path: `health`
   - Method: `GET`
3. **Add Function Node**:
```javascript
return {
  status: 'active',
  timestamp: new Date().toISOString(),
  service: 'n8n-payment-processor',
  version: '1.0.0'
};
```

## Testing the Workflows

### 1. Test Health Check
```bash
curl https://n8n-vaw3sogm.runner.web.id/webhook/health
```

### 2. Test Payment Creation
```bash
curl -X POST https://n8n-vaw3sogm.runner.web.id/webhook/create-payment \
  -H "Authorization: Bearer your_jwt_token" \
  -H "X-User-ID: user_uuid" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 50000,
    "userId": "user_uuid",
    "userEmail": "<EMAIL>",
    "description": "Test top-up"
  }'
```

### 3. Test Frontend Integration
In your browser console:
```javascript
testN8nWorkflows()
```

## Security Checklist

- [ ] All environment variables configured
- [ ] Webhook authentication enabled
- [ ] HTTPS endpoints only
- [ ] Token validation implemented
- [ ] Error handling configured
- [ ] Logging enabled for debugging

## Next Steps

1. **Configure Xendit Webhook**: Point to `https://n8n-vaw3sogm.runner.web.id/webhook/xendit-payment`
2. **Test Payment Flow**: Create test payment and verify completion
3. **Monitor Executions**: Check n8n execution logs for errors
4. **Update Frontend**: Ensure frontend uses new secure endpoints
