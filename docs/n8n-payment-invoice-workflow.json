{"name": "Payment Invoice Creation", "nodes": [{"parameters": {"httpMethod": "POST", "path": "create-payment", "responseMode": "responseNode", "options": {}}, "id": "f6b4c5d7-8e9f-4a1b-2c3d-4e5f6a7b8c9d", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "create-payment"}, {"parameters": {"functionCode": "// TEMPORARY: Bypass auth for testing\nconsole.log('🧪 BYPASSING AUTH VALIDATION FOR TESTING');\n\nconst inputData = $input.first() || {};\nconst body = inputData.body || {};\n\nconsole.log('Input received:', inputData);\nconsole.log('Body received:', body);\n\n// Return success without validation\nreturn {\n  json: {\n    success: true,\n    token: 'test-token',\n    userId: body.userId || '5c06efc4-061d-4b3b-9f11-329f5a9de851',\n    amount: parseInt(body.amount) || 50000,\n    userEmail: body.userEmail || '<EMAIL>',\n    description: body.description || `Top-up balance - Rp ${body.amount}`,\n    externalId: `topup_${body.userId || 'test'}_${Date.now()}`,\n    timestamp: new Date().toISOString()\n  }\n};"}, "id": "a1b2c3d4-e5f6-7a8b-9c0d-1e2f3a4b5c6d", "name": "Validate Authentication", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.success}}", "value2": true}]}}, "id": "b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e", "name": "Check Authentication", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"url": "https://api.xendit.co/v2/invoices", "authentication": "none", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Authorization", "value": "Basic eG5kX2RldmVsb3BtZW50X1h6T3o1MWN2NUNyeVY1YlFXeXR2WlFaRmhqRkZLWm5xYzlNeWd2T25CYWl0MFF2UTFVdk9sU1JFdTlUS3E6"}]}, "sendBody": true, "jsonParameters": true, "bodyParametersJson": "{\n  \"external_id\": \"{{$node['Validate Authentication'].json.externalId}}\",\n  \"amount\": {{$node['Validate Authentication'].json.amount}},\n  \"description\": \"{{$node['Validate Authentication'].json.description}}\",\n  \"invoice_duration\": 86400,\n  \"customer\": {\n    \"email\": \"{{$node['Validate Authentication'].json.userEmail}}\"\n  },\n  \"success_redirect_url\": \"http://localhost:3000/dashboard/billing?payment=success\",\n  \"failure_redirect_url\": \"http://localhost:3000/dashboard/billing?payment=failed\",\n  \"currency\": \"IDR\",\n  \"items\": [\n    {\n      \"name\": \"Balance Top-up\",\n      \"quantity\": 1,\n      \"price\": {{$node['Validate Authentication'].json.amount}}\n    }\n  ]\n}", "options": {}}, "id": "c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f", "name": "Create Xendit Invoice", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200]}, {"parameters": {"method": "POST", "url": "https://iozuzywvrncfejmlnsfu.supabase.co/rest/v1/payments", "authentication": "none", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlvenV6eXd2cm5jZmVqbWxuc2Z1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkyMzI2NiwiZXhwIjoyMDY2NDk5MjY2fQ.VdNN7_RpZdu_HN2VHGrR3pFAqz32mKRtazgdet5am6s"}, {"name": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlvenV6eXd2cm5jZmVqbWxuc2Z1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkyMzI2NiwiZXhwIjoyMDY2NDk5MjY2fQ.VdNN7_RpZdu_HN2VHGrR3pFAqz32mKRtazgdet5am6s"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyContentType": "json", "jsonParameters": false, "bodyParametersJson": "{\n  \"user_id\": \"5c06efc4-061d-4b3b-9f11-329f5a9de851\",\n  \"amount\": 50000,\n  \"credits\": 50000,\n  \"status\": \"PENDING\",\n  \"external_id\": \"topup_test_{{Date.now()}}\",\n  \"invoice_id\": \"test_invoice_{{Date.now()}}\",\n  \"invoice_url\": \"https://checkout-staging.xendit.co/web/test\",\n  \"date\": \"{{new Date().toISOString().split('T')[0]}}\"\n}", "options": {}}, "id": "d4e5f6a7-b8c9-0d1e-2f3a-4b5c6d7e8f9a", "name": "Store Payment Record", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 200]}, {"parameters": {"functionCode": "// Format successful payment creation response\nconst authData = $node['Validate Authentication'].json;\nconst xenditData = $node['Create Xendit Invoice'].json;\nconst supabaseData = $node['Store Payment Record'].json;\n\nreturn {\n  json: {\n    success: true,\n    paymentUrl: xenditData.invoice_url,\n    paymentId: xenditData.id,\n    invoiceId: xenditData.id,\n    amount: authData.amount,\n    externalId: authData.externalId,\n    userId: authData.userId,\n    message: 'Payment invoice created successfully',\n    createdAt: new Date().toISOString(),\n    xenditResponse: xenditData,\n    databaseRecord: supabaseData\n  }\n};"}, "id": "e5f6a7b8-c9d0-1e2f-3a4b-5c6d7e8f9a0b", "name": "Format Success Response", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1340, 200]}, {"parameters": {"functionCode": "// Format error response for authentication failures\nconst authData = $node['Validate Authentication'].json;\n\nreturn {\n  json: {\n    success: false,\n    error: authData.error,\n    message: authData.message,\n    redirectToLogin: authData.redirectToLogin || false,\n    statusCode: authData.statusCode || 400,\n    timestamp: new Date().toISOString()\n  }\n};"}, "id": "f6a7b8c9-d0e1-2f3a-4b5c-6d7e8f9a0b1c", "name": "Format Error Response", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{$json}}"}, "id": "a7b8c9d0-e1f2-3a4b-5c6d-7e8f9a0b1c2d", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Validate Authentication", "type": "main", "index": 0}]]}, "Validate Authentication": {"main": [[{"node": "Check Authentication", "type": "main", "index": 0}]]}, "Check Authentication": {"main": [[{"node": "Create Xendit Invoice", "type": "main", "index": 0}], [{"node": "Format Error Response", "type": "main", "index": 0}]]}, "Create Xendit Invoice": {"main": [[{"node": "Store Payment Record", "type": "main", "index": 0}]]}, "Store Payment Record": {"main": [[{"node": "Format Success Response", "type": "main", "index": 0}]]}, "Format Success Response": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Format Error Response": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "id": "payment-invoice-creation", "meta": {"instanceId": "n8n-payment-invoice"}, "tags": []}