import type { <PERSON><PERSON>, HandlerEvent, HandlerContext } from '@netlify/functions';
import { createClient } from '@supabase/supabase-js';
import crypto from 'node:crypto';

// Initialize Supabase client with service role key for server-side operations
const supabaseUrl = process.env.VITE_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

const handler: Handler = async (event: HandlerEvent, context: HandlerContext) => {
  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      body: JSON.stringify({ error: 'Method not allowed' }),
    };
  }

  try {
    const body = event.body;
    if (!body) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Request body is required' }),
      };
    }

    // Validate webhook signature for security
    const signature = event.headers['x-callback-token'] || '';
    const webhookToken = process.env.XENDIT_WEBHOOK_TOKEN;
    
    if (webhookToken && !validateWebhookSignature(body, signature, webhookToken)) {
      console.error('Invalid webhook signature');
      return {
        statusCode: 401,
        body: JSON.stringify({ error: 'Invalid signature' }),
      };
    }

    const webhookData = JSON.parse(body);
    console.log('Received webhook:', webhookData);

    // Only process invoice.paid events
    if (webhookData.status !== 'PAID') {
      console.log(`Ignoring webhook for status: ${webhookData.status}`);
      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Webhook received but not processed' }),
      };
    }

    const { id: invoiceId, status, external_id, amount, paid_at } = webhookData;

    // Check if payment already processed (idempotency) using invoice_id
    const { data: existingPayment } = await supabase
      .from('payments')
      .select('status')
      .eq('invoice_id', invoiceId)
      .single();

    if (existingPayment?.status === 'completed') {
      console.log(`Payment ${invoiceId} already processed`);
      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Payment already processed' }),
      };
    }

    // Extract user_id from external_id (format: topup_userId_timestamp)
    const userIdMatch = external_id.match(/^topup_([^_]+)_/);
    if (!userIdMatch) {
      throw new Error('Invalid external_id format');
    }
    const userId = userIdMatch[1];

    // Process payment completion
    await processPaymentCompletion(invoiceId, userId, amount, paid_at);

    return {
      statusCode: 200,
      body: JSON.stringify({ 
        message: 'Webhook processed successfully',
        invoiceId,
        userId,
        amount 
      }),
    };
  } catch (error) {
    console.error('Webhook processing error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }),
    };
  }
};

async function updateUserBalance(userId: string, amountToAdd: number): Promise<number> {
  // First, try to get the current balance
  const { data: existingBalance, error: selectError } = await supabase
    .from('balances')
    .select('current_balance')
    .eq('user_id', userId)
    .single();

  let newBalance: number;

  if (selectError && selectError.code === 'PGRST116') {
    // No existing balance record, create one
    newBalance = amountToAdd;

    const { error: insertError } = await supabase
      .from('balances')
      .insert({
        user_id: userId,
        current_balance: newBalance,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (insertError) {
      throw new Error(`Failed to create balance record: ${insertError.message}`);
    }
  } else if (selectError) {
    // Some other error occurred
    throw new Error(`Failed to fetch balance: ${selectError.message}`);
  } else {
    // Balance record exists, update it
    newBalance = (existingBalance?.current_balance || 0) + amountToAdd;

    const { error: updateError } = await supabase
      .from('balances')
      .update({
        current_balance: newBalance,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId);

    if (updateError) {
      throw new Error(`Failed to update balance: ${updateError.message}`);
    }
  }

  console.log(`✅ Balance updated for user ${userId}: +${amountToAdd} = ${newBalance}`);
  return newBalance;
}

async function processPaymentCompletion(invoiceId: string, userId: string, amount: number, paidAt?: string) {
  try {
    console.log(`🔄 Processing payment completion: ${invoiceId} for user ${userId}, amount: ${amount}`);

    // 1. Update payment status using invoice_id (not id)
    const { error: paymentError } = await supabase
      .from('payments')
      .update({
        status: 'completed',
        paid_at: paidAt || new Date().toISOString(),
      })
      .eq('invoice_id', invoiceId);

    if (paymentError) {
      throw new Error(`Failed to update payment: ${paymentError.message}`);
    }

    // 2. Update user balance using the safe utility function
    const newBalance = await updateUserBalance(userId, amount);

    // 3. Create transaction record with proper format
    const { error: transactionError } = await supabase
      .from('transactions')
      .insert({
        user_id: userId,
        type: 'purchase', // Top-up menggunakan type 'purchase'
        amount: amount,
        description: `Credit purchase: ${amount} credits`,
        date: new Date().toISOString().split('T')[0], // DATE format
      });

    if (transactionError) {
      throw new Error(`Failed to create transaction: ${transactionError.message}`);
    }

    console.log(`✅ Payment ${invoiceId} completed successfully. New balance: ${newBalance}`);
  } catch (error) {
    console.error('❌ Error in processPaymentCompletion:', error);
    throw error;
  }
}

function validateWebhookSignature(payload: string, signature: string, webhookToken: string): boolean {
  try {
    // Xendit uses HMAC-SHA256 for webhook signature validation
    const expectedSignature = crypto
      .createHmac('sha256', webhookToken)
      .update(payload)
      .digest('hex');

    return signature === expectedSignature;
  } catch (error) {
    console.error('Error validating webhook signature:', error);
    return false;
  }
}

export { handler };
